lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:

  .:
    devDependencies:
      '@types/node':
        specifier: ^18.15.0
        version: 18.19.123
      changelogen:
        specifier: ^0.6.2
        version: 0.6.2
      git-cz:
        specifier: ^4.9.0
        version: 4.9.0
      http-server:
        specifier: ^14.1.1
        version: 14.1.1
      husky:
        specifier: ^8.0.0
        version: 8.0.3
      robuild:
        specifier: ^0.0.5
        version: 0.0.5
      turbo:
        specifier: ^2.5.6
        version: 2.5.6
      typescript:
        specifier: ^5.0.0
        version: 5.8.3

  playground:
    dependencies:
      browser-storage-lru-cleaner:
        specifier: workspace:*
        version: link:..
      huse:
        specifier: ^2.0.4
        version: 2.0.4(react@19.1.1)
      react:
        specifier: ^19.1.1
        version: 19.1.1
      react-dom:
        specifier: ^19.1.1
        version: 19.1.1(react@19.1.1)
    devDependencies:
      '@eslint/js':
        specifier: ^9.33.0
        version: 9.34.0
      '@types/react':
        specifier: ^19.1.10
        version: 19.1.11
      '@types/react-dom':
        specifier: ^19.1.7
        version: 19.1.8(@types/react@19.1.11)
      '@vitejs/plugin-react':
        specifier: ^5.0.0
        version: 5.0.1(vite@7.1.3(@types/node@18.19.123)(jiti@2.5.1)(yaml@2.8.1))
      eslint:
        specifier: ^9.33.0
        version: 9.34.0(jiti@2.5.1)
      eslint-plugin-react-hooks:
        specifier: ^5.2.0
        version: 5.2.0(eslint@9.34.0(jiti@2.5.1))
      eslint-plugin-react-refresh:
        specifier: ^0.4.20
        version: 0.4.20(eslint@9.34.0(jiti@2.5.1))
      globals:
        specifier: ^16.3.0
        version: 16.3.0
      typescript:
        specifier: ~5.8.3
        version: 5.8.3
      typescript-eslint:
        specifier: ^8.39.1
        version: 8.41.0(eslint@9.34.0(jiti@2.5.1))(typescript@5.8.3)
      vite:
        specifier: ^7.1.2
        version: 7.1.3(@types/node@18.19.123)(jiti@2.5.1)(yaml@2.8.1)

packages:

  '@ampproject/remapping@2.3.0':
    resolution: {integrity: sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw==}
    engines: {node: '>=6.0.0'}

  '@babel/code-frame@7.27.1':
    resolution: {integrity: sha512-cjQ7ZlQ0Mv3b47hABuTevyTuYN4i+loJKGeV9flcCgIK37cCXRh+L1bd3iBHlynerhQ7BhCkn2BPbQUL+rGqFg==}
    engines: {node: '>=6.9.0'}

  '@babel/compat-data@7.28.0':
    resolution: {integrity: sha512-60X7qkglvrap8mn1lh2ebxXdZYtUcpd7gsmy9kLaBJ4i/WdY8PqTSdxyA8qraikqKQK5C1KRBKXqznrVapyNaw==}
    engines: {node: '>=6.9.0'}

  '@babel/core@7.28.3':
    resolution: {integrity: sha512-yDBHV9kQNcr2/sUr9jghVyz9C3Y5G2zUM2H2lo+9mKv4sFgbA8s8Z9t8D1jiTkGoO/NoIfKMyKWr4s6CN23ZwQ==}
    engines: {node: '>=6.9.0'}

  '@babel/generator@7.28.3':
    resolution: {integrity: sha512-3lSpxGgvnmZznmBkCRnVREPUFJv2wrv9iAoFDvADJc0ypmdOxdUtcLeBgBJ6zE0PMeTKnxeQzyk0xTBq4Ep7zw==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-compilation-targets@7.27.2':
    resolution: {integrity: sha512-2+1thGUUWWjLTYTHZWK1n8Yga0ijBz1XAhUXcKy81rd5g6yh7hGqMp45v7cadSbEHc9G3OTv45SyneRN3ps4DQ==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-globals@7.28.0':
    resolution: {integrity: sha512-+W6cISkXFa1jXsDEdYA8HeevQT/FULhxzR99pxphltZcVaugps53THCeiWA8SguxxpSp3gKPiuYfSWopkLQ4hw==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-module-imports@7.27.1':
    resolution: {integrity: sha512-0gSFWUPNXNopqtIPQvlD5WgXYI5GY2kP2cCvoT8kczjbfcfuIljTbcWrulD1CIPIX2gt1wghbDy08yE1p+/r3w==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-module-transforms@7.28.3':
    resolution: {integrity: sha512-gytXUbs8k2sXS9PnQptz5o0QnpLL51SwASIORY6XaBKF88nsOT0Zw9szLqlSGQDP/4TljBAD5y98p2U1fqkdsw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-plugin-utils@7.27.1':
    resolution: {integrity: sha512-1gn1Up5YXka3YYAHGKpbideQ5Yjf1tDa9qYcgysz+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-string-parser@7.27.1':
    resolution: {integrity: sha512-qMlSxKbpRlAridDExk92nSobyDdpPijUq2DW6oDnUqd0iOGxmQjyqhMIihI9+zv4LPyZdRje2cavWPbCbWm3eA==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-identifier@7.27.1':
    resolution: {integrity: sha512-D2hP9eA+Sqx1kBZgzxZh0y1trbuU+JoDkiEwqhQ36nodYqJwyEIhPSdMNd7lOm/4io72luTPWH20Yda0xOuUow==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-option@7.27.1':
    resolution: {integrity: sha512-YvjJow9FxbhFFKDSuFnVCe2WxXk1zWc22fFePVNEaWJEu8IrZVlda6N0uHwzZrUM1il7NC9Mlp4MaJYbYd9JSg==}
    engines: {node: '>=6.9.0'}

  '@babel/helpers@7.28.3':
    resolution: {integrity: sha512-PTNtvUQihsAsDHMOP5pfobP8C6CM4JWXmP8DrEIt46c3r2bf87Ua1zoqevsMo9g+tWDwgWrFP5EIxuBx5RudAw==}
    engines: {node: '>=6.9.0'}

  '@babel/parser@7.28.3':
    resolution: {integrity: sha512-7+Ey1mAgYqFAx2h0RuoxcQT5+MlG3GTV0TQrgr7/ZliKsm/MNDxVVutlWaziMq7wJNAz8MTqz55XLpWvva6StA==}
    engines: {node: '>=6.0.0'}
    hasBin: true

  '@babel/parser@7.28.4':
    resolution: {integrity: sha512-yZbBqeM6TkpP9du/I2pUZnJsRMGGvOuIrhjzC1AwHwW+6he4mni6Bp/m8ijn0iOuZuPI2BfkCoSRunpyjnrQKg==}
    engines: {node: '>=6.0.0'}
    hasBin: true

  '@babel/plugin-transform-react-jsx-self@7.27.1':
    resolution: {integrity: sha512-6UzkCs+ejGdZ5mFFC/OCUrv028ab2fp1znZmCZjAOBKiBK2jXD1O+BPSfX8X2qjJ75fZBMSnQn3Rq2mrBJK2mw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-react-jsx-source@7.27.1':
    resolution: {integrity: sha512-zbwoTsBruTeKB9hSq73ha66iFeJHuaFkUbwvqElnygoNbj/jHRsSeokowZFN3CZ64IvEqcmmkVe89OPXc7ldAw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/template@7.27.2':
    resolution: {integrity: sha512-LPDZ85aEJyYSd18/DkjNh4/y1ntkE5KwUHWTiqgRxruuZL2F1yuHligVHLvcHY2vMHXttKFpJn6LwfI7cw7ODw==}
    engines: {node: '>=6.9.0'}

  '@babel/traverse@7.28.3':
    resolution: {integrity: sha512-7w4kZYHneL3A6NP2nxzHvT3HCZ7puDZZjFMqDpBPECub79sTtSO5CGXDkKrTQq8ksAwfD/XI2MRFX23njdDaIQ==}
    engines: {node: '>=6.9.0'}

  '@babel/types@7.28.2':
    resolution: {integrity: sha512-ruv7Ae4J5dUYULmeXw1gmb7rYRz57OWCPM57pHojnLq/3Z1CK2lNSLTCVjxVk1F/TZHwOZZrOWi0ur95BbLxNQ==}
    engines: {node: '>=6.9.0'}

  '@babel/types@7.28.4':
    resolution: {integrity: sha512-bkFqkLhh3pMBUQQkpVgWDWq/lqzc2678eUyDlTBhRqhCHFguYYGM0Efga7tYk4TogG/3x0EEl66/OQ+WGbWB/Q==}
    engines: {node: '>=6.9.0'}

  '@emnapi/core@1.5.0':
    resolution: {integrity: sha512-sbP8GzB1WDzacS8fgNPpHlp6C9VZe+SJP3F90W9rLemaQj2PzIuTEl1qDOYQf58YIpyjViI24y9aPWCjEzY2cg==}

  '@emnapi/runtime@1.5.0':
    resolution: {integrity: sha512-97/BJ3iXHww3djw6hYIfErCZFee7qCtrneuLa20UXFCOTCfBM2cvQHjWJ2EG0s0MtdNwInarqCTz35i4wWXHsQ==}

  '@emnapi/wasi-threads@1.1.0':
    resolution: {integrity: sha512-WI0DdZ8xFSbgMjR1sFsKABJ/C5OnRrjT06JXbZKexJGrDuPTzZdDYfFlsgcCXCyf+suG5QU2e/y1Wo2V/OapLQ==}

  '@esbuild/aix-ppc64@0.25.9':
    resolution: {integrity: sha512-OaGtL73Jck6pBKjNIe24BnFE6agGl+6KxDtTfHhy1HmhthfKouEcOhqpSL64K4/0WCtbKFLOdzD/44cJ4k9opA==}
    engines: {node: '>=18'}
    cpu: [ppc64]
    os: [aix]

  '@esbuild/android-arm64@0.25.9':
    resolution: {integrity: sha512-IDrddSmpSv51ftWslJMvl3Q2ZT98fUSL2/rlUXuVqRXHCs5EUF1/f+jbjF5+NG9UffUDMCiTyh8iec7u8RlTLg==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [android]

  '@esbuild/android-arm@0.25.9':
    resolution: {integrity: sha512-5WNI1DaMtxQ7t7B6xa572XMXpHAaI/9Hnhk8lcxF4zVN4xstUgTlvuGDorBguKEnZO70qwEcLpfifMLoxiPqHQ==}
    engines: {node: '>=18'}
    cpu: [arm]
    os: [android]

  '@esbuild/android-x64@0.25.9':
    resolution: {integrity: sha512-I853iMZ1hWZdNllhVZKm34f4wErd4lMyeV7BLzEExGEIZYsOzqDWDf+y082izYUE8gtJnYHdeDpN/6tUdwvfiw==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [android]

  '@esbuild/darwin-arm64@0.25.9':
    resolution: {integrity: sha512-XIpIDMAjOELi/9PB30vEbVMs3GV1v2zkkPnuyRRURbhqjyzIINwj+nbQATh4H9GxUgH1kFsEyQMxwiLFKUS6Rg==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [darwin]

  '@esbuild/darwin-x64@0.25.9':
    resolution: {integrity: sha512-jhHfBzjYTA1IQu8VyrjCX4ApJDnH+ez+IYVEoJHeqJm9VhG9Dh2BYaJritkYK3vMaXrf7Ogr/0MQ8/MeIefsPQ==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [darwin]

  '@esbuild/freebsd-arm64@0.25.9':
    resolution: {integrity: sha512-z93DmbnY6fX9+KdD4Ue/H6sYs+bhFQJNCPZsi4XWJoYblUqT06MQUdBCpcSfuiN72AbqeBFu5LVQTjfXDE2A6Q==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [freebsd]

  '@esbuild/freebsd-x64@0.25.9':
    resolution: {integrity: sha512-mrKX6H/vOyo5v71YfXWJxLVxgy1kyt1MQaD8wZJgJfG4gq4DpQGpgTB74e5yBeQdyMTbgxp0YtNj7NuHN0PoZg==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [freebsd]

  '@esbuild/linux-arm64@0.25.9':
    resolution: {integrity: sha512-BlB7bIcLT3G26urh5Dmse7fiLmLXnRlopw4s8DalgZ8ef79Jj4aUcYbk90g8iCa2467HX8SAIidbL7gsqXHdRw==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [linux]

  '@esbuild/linux-arm@0.25.9':
    resolution: {integrity: sha512-HBU2Xv78SMgaydBmdor38lg8YDnFKSARg1Q6AT0/y2ezUAKiZvc211RDFHlEZRFNRVhcMamiToo7bDx3VEOYQw==}
    engines: {node: '>=18'}
    cpu: [arm]
    os: [linux]

  '@esbuild/linux-ia32@0.25.9':
    resolution: {integrity: sha512-e7S3MOJPZGp2QW6AK6+Ly81rC7oOSerQ+P8L0ta4FhVi+/j/v2yZzx5CqqDaWjtPFfYz21Vi1S0auHrap3Ma3A==}
    engines: {node: '>=18'}
    cpu: [ia32]
    os: [linux]

  '@esbuild/linux-loong64@0.25.9':
    resolution: {integrity: sha512-Sbe10Bnn0oUAB2AalYztvGcK+o6YFFA/9829PhOCUS9vkJElXGdphz0A3DbMdP8gmKkqPmPcMJmJOrI3VYB1JQ==}
    engines: {node: '>=18'}
    cpu: [loong64]
    os: [linux]

  '@esbuild/linux-mips64el@0.25.9':
    resolution: {integrity: sha512-YcM5br0mVyZw2jcQeLIkhWtKPeVfAerES5PvOzaDxVtIyZ2NUBZKNLjC5z3/fUlDgT6w89VsxP2qzNipOaaDyA==}
    engines: {node: '>=18'}
    cpu: [mips64el]
    os: [linux]

  '@esbuild/linux-ppc64@0.25.9':
    resolution: {integrity: sha512-++0HQvasdo20JytyDpFvQtNrEsAgNG2CY1CLMwGXfFTKGBGQT3bOeLSYE2l1fYdvML5KUuwn9Z8L1EWe2tzs1w==}
    engines: {node: '>=18'}
    cpu: [ppc64]
    os: [linux]

  '@esbuild/linux-riscv64@0.25.9':
    resolution: {integrity: sha512-uNIBa279Y3fkjV+2cUjx36xkx7eSjb8IvnL01eXUKXez/CBHNRw5ekCGMPM0BcmqBxBcdgUWuUXmVWwm4CH9kg==}
    engines: {node: '>=18'}
    cpu: [riscv64]
    os: [linux]

  '@esbuild/linux-s390x@0.25.9':
    resolution: {integrity: sha512-Mfiphvp3MjC/lctb+7D287Xw1DGzqJPb/J2aHHcHxflUo+8tmN/6d4k6I2yFR7BVo5/g7x2Monq4+Yew0EHRIA==}
    engines: {node: '>=18'}
    cpu: [s390x]
    os: [linux]

  '@esbuild/linux-x64@0.25.9':
    resolution: {integrity: sha512-iSwByxzRe48YVkmpbgoxVzn76BXjlYFXC7NvLYq+b+kDjyyk30J0JY47DIn8z1MO3K0oSl9fZoRmZPQI4Hklzg==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [linux]

  '@esbuild/netbsd-arm64@0.25.9':
    resolution: {integrity: sha512-9jNJl6FqaUG+COdQMjSCGW4QiMHH88xWbvZ+kRVblZsWrkXlABuGdFJ1E9L7HK+T0Yqd4akKNa/lO0+jDxQD4Q==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [netbsd]

  '@esbuild/netbsd-x64@0.25.9':
    resolution: {integrity: sha512-RLLdkflmqRG8KanPGOU7Rpg829ZHu8nFy5Pqdi9U01VYtG9Y0zOG6Vr2z4/S+/3zIyOxiK6cCeYNWOFR9QP87g==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [netbsd]

  '@esbuild/openbsd-arm64@0.25.9':
    resolution: {integrity: sha512-YaFBlPGeDasft5IIM+CQAhJAqS3St3nJzDEgsgFixcfZeyGPCd6eJBWzke5piZuZ7CtL656eOSYKk4Ls2C0FRQ==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [openbsd]

  '@esbuild/openbsd-x64@0.25.9':
    resolution: {integrity: sha512-1MkgTCuvMGWuqVtAvkpkXFmtL8XhWy+j4jaSO2wxfJtilVCi0ZE37b8uOdMItIHz4I6z1bWWtEX4CJwcKYLcuA==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [openbsd]

  '@esbuild/openharmony-arm64@0.25.9':
    resolution: {integrity: sha512-4Xd0xNiMVXKh6Fa7HEJQbrpP3m3DDn43jKxMjxLLRjWnRsfxjORYJlXPO4JNcXtOyfajXorRKY9NkOpTHptErg==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [openharmony]

  '@esbuild/sunos-x64@0.25.9':
    resolution: {integrity: sha512-WjH4s6hzo00nNezhp3wFIAfmGZ8U7KtrJNlFMRKxiI9mxEK1scOMAaa9i4crUtu+tBr+0IN6JCuAcSBJZfnphw==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [sunos]

  '@esbuild/win32-arm64@0.25.9':
    resolution: {integrity: sha512-mGFrVJHmZiRqmP8xFOc6b84/7xa5y5YvR1x8djzXpJBSv/UsNK6aqec+6JDjConTgvvQefdGhFDAs2DLAds6gQ==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [win32]

  '@esbuild/win32-ia32@0.25.9':
    resolution: {integrity: sha512-b33gLVU2k11nVx1OhX3C8QQP6UHQK4ZtN56oFWvVXvz2VkDoe6fbG8TOgHFxEvqeqohmRnIHe5A1+HADk4OQww==}
    engines: {node: '>=18'}
    cpu: [ia32]
    os: [win32]

  '@esbuild/win32-x64@0.25.9':
    resolution: {integrity: sha512-PPOl1mi6lpLNQxnGoyAfschAodRFYXJ+9fs6WHXz7CSWKbOqiMZsubC+BQsVKuul+3vKLuwTHsS2c2y9EoKwxQ==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [win32]

  '@eslint-community/eslint-utils@4.7.0':
    resolution: {integrity: sha512-dyybb3AcajC7uha6CvhdVRJqaKyn7w2YKqKyAN37NKYgZT36w+iRb0Dymmc5qEJ549c/S31cMMSFd75bteCpCw==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: ^6.0.0 || ^7.0.0 || >=8.0.0

  '@eslint-community/regexpp@4.12.1':
    resolution: {integrity: sha512-CCZCDJuduB9OUkFkY2IgppNZMi2lBQgD2qzwXkEia16cge2pijY/aXi96CJMquDMn3nJdlPV1A5KrJEXwfLNzQ==}
    engines: {node: ^12.0.0 || ^14.0.0 || >=16.0.0}

  '@eslint/config-array@0.21.0':
    resolution: {integrity: sha512-ENIdc4iLu0d93HeYirvKmrzshzofPw6VkZRKQGe9Nv46ZnWUzcF1xV01dcvEg/1wXUR61OmmlSfyeyO7EvjLxQ==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/config-helpers@0.3.1':
    resolution: {integrity: sha512-xR93k9WhrDYpXHORXpxVL5oHj3Era7wo6k/Wd8/IsQNnZUTzkGS29lyn3nAT05v6ltUuTFVCCYDEGfy2Or/sPA==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/core@0.15.2':
    resolution: {integrity: sha512-78Md3/Rrxh83gCxoUc0EiciuOHsIITzLy53m3d9UyiW8y9Dj2D29FeETqyKA+BRK76tnTp6RXWb3pCay8Oyomg==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/eslintrc@3.3.1':
    resolution: {integrity: sha512-gtF186CXhIl1p4pJNGZw8Yc6RlshoePRvE0X91oPGb3vZ8pM3qOS9W9NGPat9LziaBV7XrJWGylNQXkGcnM3IQ==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/js@9.34.0':
    resolution: {integrity: sha512-EoyvqQnBNsV1CWaEJ559rxXL4c8V92gxirbawSmVUOWXlsRxxQXl6LmCpdUblgxgSkDIqKnhzba2SjRTI/A5Rw==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/object-schema@2.1.6':
    resolution: {integrity: sha512-RBMg5FRL0I0gs51M/guSAj5/e14VQ4tpZnQNWwuDT66P14I43ItmPfIZRhO9fUVIPOAQXU47atlywZ/czoqFPA==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/plugin-kit@0.3.5':
    resolution: {integrity: sha512-Z5kJ+wU3oA7MMIqVR9tyZRtjYPr4OC004Q4Rw7pgOKUOKkJfZ3O24nz3WYfGRpMDNmcOi3TwQOmgm7B7Tpii0w==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@humanfs/core@0.19.1':
    resolution: {integrity: sha512-5DyQ4+1JEUzejeK1JGICcideyfUbGixgS9jNgex5nqkW+cY7WZhxBigmieN5Qnw9ZosSNVC9KQKyb+GUaGyKUA==}
    engines: {node: '>=18.18.0'}

  '@humanfs/node@0.16.6':
    resolution: {integrity: sha512-YuI2ZHQL78Q5HbhDiBA1X4LmYdXCKCMQIfw0pw7piHJwyREFebJUvrQN4cMssyES6x+vfUbx1CIpaQUKYdQZOw==}
    engines: {node: '>=18.18.0'}

  '@humanwhocodes/module-importer@1.0.1':
    resolution: {integrity: sha512-bxveV4V8v5Yb4ncFTT3rPSgZBOpCkjfK0y4oVVVJwIuDVBRMDXrPyXRL988i5ap9m9bnyEEjWfm5WkBmtffLfA==}
    engines: {node: '>=12.22'}

  '@humanwhocodes/retry@0.3.1':
    resolution: {integrity: sha512-JBxkERygn7Bv/GbN5Rv8Ul6LVknS+5Bp6RgDC/O8gEBU/yeH5Ui5C/OlWrTb6qct7LjjfT6Re2NxB0ln0yYybA==}
    engines: {node: '>=18.18'}

  '@humanwhocodes/retry@0.4.3':
    resolution: {integrity: sha512-bV0Tgo9K4hfPCek+aMAn81RppFKv2ySDQeMoSZuvTASywNTnVJCArCZE2FWqpvIatKu7VMRLWlR1EazvVhDyhQ==}
    engines: {node: '>=18.18'}

  '@huse/action-pending@1.1.2':
    resolution: {integrity: sha512-uitOZWivOD63hCmN3K1wbsUDNRXIrOqBhA7rcseEGq2UmPXZB17OkHncbqHP1x4yNUrMwnPQn9MTVPruFMNvtA==}
    peerDependencies:
      react: '>=16.8.0'

  '@huse/boolean@1.2.0':
    resolution: {integrity: sha512-ZsC+RfXyrg9bhdY9+B+4RJh9fwXCUL7Cwh0lvxintCnqgjCb1mT0j7Pjcrg1jwzoeRrRWsHPcm1oW/8Gc8+aFw==}
    peerDependencies:
      react: '>=16.8.0'

  '@huse/click-outside@1.1.1':
    resolution: {integrity: sha512-jR+8K9iaPdHlWsbm3Ka5NyWtjkITzKtiRIq62YSA0Cv6s+M37uthpxuo0rBOny1uCnSeRZa2WPJslxhsVIt+Ug==}
    peerDependencies:
      react: '>=16.8.0'

  '@huse/collection@1.1.2':
    resolution: {integrity: sha512-3bwtHjHWOhVEoXncMiIR4UmxNKP/rHBgD/KP+uVehBuhx83nfulfuuziswPYOfMTmE+z2BCdq65DppOZM11Buw==}
    peerDependencies:
      react: '>=16.8.0'

  '@huse/debounce@1.1.2':
    resolution: {integrity: sha512-NBJqyHd3XmJA7PghtHh+BCSyR7zt4wD0w1ylnogyf9eH2XzyCsKrKJYe6Zv6OImkFHXkpqenU0UoZi2r7jJKOw==}
    peerDependencies:
      react: '>=16.8.0'

  '@huse/debug@1.1.1':
    resolution: {integrity: sha512-bhgH1O0M0neKMw9OcWhFJlH5sXdVqWkn80Sa1aT+QI/2e3RhdKA6l1xXZR7pIZwZ+5KP4I+vT3i0B4jdtdBLKA==}
    peerDependencies:
      react: '>=16.8.0'

  '@huse/derived-state@1.1.1':
    resolution: {integrity: sha512-y/Auo/xdrS4oXEaaNl0dWDm3EEUUOmaxWWVtypv7oyEh7loP2VnmhGnia6RvInI/edGiqMumFtE4XF5Lt7z0yg==}
    peerDependencies:
      react: '>=16.8.0'

  '@huse/document-event@1.1.1':
    resolution: {integrity: sha512-KKk0gBiZd7L7sg8h8BU+am/iG6Bx7IPGXc1/rUMX+of9X4OmLl/+igog5wt5c+MZOzetWks/oiulxxZg1/qmRg==}
    peerDependencies:
      react: '>=16.8.0'

  '@huse/document-title@1.1.1':
    resolution: {integrity: sha512-v6DohedNn2YN0lbwzTdALQ+dOfw2gXEyYx5XKaRWYVbk1HOmCzS7ELK6g9zAi5Rnz/H7fA9RO0I30UATUtWJiQ==}
    peerDependencies:
      react: '>=16.8.0'

  '@huse/effect-ref@1.1.1':
    resolution: {integrity: sha512-WVMw6kGIFjzPOnCvByxNQYAl0bEeXy3iI43y7JAHY78uGsxKbH0yL6MOvqCWlKyRUx7iHRuNrPjtGogGIueizw==}
    peerDependencies:
      react: '>=16.8.0'

  '@huse/element-size@1.1.1':
    resolution: {integrity: sha512-vuUS8tgfhv+hST7JCns0vLNjBRTP3fk5piCWkx3gW5W1y+TSzIWEwACuHmWJIHKmkcpKwS1IQYtvv0RzBv4c6A==}
    peerDependencies:
      react: '>=16.8.0'

  '@huse/hover@1.1.3':
    resolution: {integrity: sha512-dOagOcxSr+88EbBIaBg2w9AiYFtZucjHug+VlZ4SSZrH2FKDX/MgE8Gtx9Mm2aClUfEnYJ/5gttAlBDAhqiGbA==}
    peerDependencies:
      react: '>=16.8.0'

  '@huse/infinite-scroll@1.2.2':
    resolution: {integrity: sha512-rgXd6wFU3Ib+sqgq4ZqykJttZKkTfYg34c+jHR4bWCnXOSi78VeUqNWhm0z8CvTizMYgwP3OALNM/gaGjXpEdA==}
    peerDependencies:
      react: '>=16.8.0'

  '@huse/input-value@1.1.1':
    resolution: {integrity: sha512-j7F9006zILVdo2tFzBexhReXxQqJBqELrQQIG0G88u8zHid/Ltm0tfRjh+VKXFcUu3bAFjWdZOrKk2Eqf+9h1w==}
    peerDependencies:
      react: '>=16.8.0'

  '@huse/intersection@1.2.1':
    resolution: {integrity: sha512-2lalFd6J2MoVWmTD+SOVgidF97jMJL35FkyzGjjqCK0dfqyLOe1tCC85tR1V2RdvL2QEs5/tJRoDCx1nBpOijQ==}
    peerDependencies:
      react: '>=16.8.0'

  '@huse/local-storage@1.1.1':
    resolution: {integrity: sha512-p3jDOT5vXBxtKKzp/QD18ACBvi2qzlYqMbEX7PwfNZwtctX3tvBU7W2dm2IHNPlsbBCmFcve2Aw8BFzdKuOxGQ==}
    peerDependencies:
      react: '>=16.8.0'

  '@huse/media@1.1.1':
    resolution: {integrity: sha512-xwiKpolkT+40lrgbNhPFSL0ckxLL2Fj3fsLSsIRQigpMrrAZwJtyEcj65xRQI3+B8FCKYbhNSdlkl5hJBwRCNw==}
    peerDependencies:
      react: '>=16.8.0'

  '@huse/merged-ref@1.2.1':
    resolution: {integrity: sha512-aHkf8n7l42bMcppCf1Uclv6QzopVCIhfoIwix/+Vc4iXGh0e/FnMiKLASE8hVn89Em9O8CR0JxePBC0ZB9Rk7Q==}
    peerDependencies:
      react: '>=16.8.0'

  '@huse/methods@1.2.2':
    resolution: {integrity: sha512-8b8cw0uabwNiQny4ebTIsE7mu8JnPOJZqCeEeW15MZaU3d3GdODrb+nB+45fi0BFzr7asc0wVpgR5/jNu1vmfg==}
    peerDependencies:
      react: '>=16.8.0'

  '@huse/network@1.1.3':
    resolution: {integrity: sha512-rIPSFaTbj8gdYAPoH1VQtFXtTaRuu5DzmiqoOGIixVZrf915GpRUE/SFV4JpJEAuOcuxSYlMhlU4wEEzEJNpIA==}
    peerDependencies:
      react: '>=16.8.0'

  '@huse/number@1.2.2':
    resolution: {integrity: sha512-2ZLFuhhB1m2MPTVFMP3tDK+RglpnO86cPaGwIMzFOaVAaFvoF0PwH5aX66Dw7C9PLVIC/5+0raFoVhBWOwCS5w==}
    peerDependencies:
      react: '>=16.8.0'

  '@huse/optimistic@0.10.1':
    resolution: {integrity: sha512-xO3dbkL4YHtimOZ4Qz7Fjl7PN7kTac5OOvTJ0fByfXveFUqJ0krsiu3Y748z13krBEcCq7g7cG570cDDA418CQ==}
    peerDependencies:
      react: '>=16.8.0'

  '@huse/performance@1.1.1':
    resolution: {integrity: sha512-cbRJuZj4ObRctW50ZGDXgMJXaH3k9QLGwF2tehejp2620H3Q1DzEDipoZCR/Q4B6ytzbsnDqBZPZGoQqOIRy9A==}
    peerDependencies:
      react: '>=16.8.0'

  '@huse/poll@1.1.2':
    resolution: {integrity: sha512-zcTZlgbVxABJn1Sq6G6Y2QzLtqe1O1lBQYI3rJIk035TcuaP0pgrndNu7Fbwf3sPni8h7KwXQrzRlQ+U/TVHXA==}
    peerDependencies:
      react: '>=16.8.0'

  '@huse/previous-value@1.1.1':
    resolution: {integrity: sha512-NHjE02+PjkMf0FwEdg8JcAASHwUT9JIGtfu6a8Un0Hs9kIZHsoEHffX5ZMUUDHI/lqS36AewQoUuTDY4spywEg==}
    peerDependencies:
      react: '>=16.8.0'

  '@huse/request@1.3.0':
    resolution: {integrity: sha512-AR0vkIreD0eON+FkFvxq+CQn71/9T+GIuF8W6ZY/Yyjeq7WxAGqMJKpVtzI9p5KZnjpQtYZDebEEUmCaiGhYRQ==}
    peerDependencies:
      react: '>=16.8.0'

  '@huse/script@1.1.1':
    resolution: {integrity: sha512-ZxdZ5wD0RQlN7BUo9eaEWW630c23wM37yWHVF7uclTMQhCksdpVweuhPC5j+yf3FWvJDceoRfgm+TG5J6drfVg==}
    peerDependencies:
      react: '>=16.8.0'

  '@huse/scroll-into-view@1.1.1':
    resolution: {integrity: sha512-Tj7jv8nO3EOLO8/fuSniIfx1RFkqY/jboF/5K/LBzDSY4LJP/1teDi+cQmObWYQXxFuhGg/KUgd7quLe3Zny4Q==}
    peerDependencies:
      react: '>=16.8.0'

  '@huse/scroll-lock@1.1.1':
    resolution: {integrity: sha512-xaAbSJ3Fih0D7g4C3Rj60YgVUclKJt0l7CaUjoXRf9Wli6NjlfV1xJC0JAXGE2s04FIyKHK0BVKTqhTHOmPIfQ==}
    peerDependencies:
      react: '>=16.8.0'

  '@huse/scroll-position@1.1.1':
    resolution: {integrity: sha512-lSIkd7SjyrqgiZb3XP0EAF4ggTFOPPkbqDgVggRvGW3cO+9s9xfQe113ZWuAkLqbNawxi+Y1Uq2tLPiOjxwUMQ==}
    peerDependencies:
      react: '>=16.8.0'

  '@huse/selection@1.1.2':
    resolution: {integrity: sha512-Db1zo2p9fRGCVB1MIz0i46AQauIScqRYi1hd2QhbRxBD2YdMHAULpPYO+EFjPNGTVmzJTeXcpin5s0FQuFeHsw==}
    peerDependencies:
      react: '>=16.8.0'

  '@huse/snapshot@1.1.2':
    resolution: {integrity: sha512-b/mFu4WK5tOdOLsAdmlW4yynkKOOw9Uk3h7/k64ExHquHeA9wMqxBgfU8yvMhw5th0f2NeOm8JiRJMLnLkR2/g==}
    peerDependencies:
      react: '>=16.8.0'

  '@huse/timeout@1.1.1':
    resolution: {integrity: sha512-Y4HTnLq07NAsmXCBLujDkHDALOfNKr8kbi7Ydyrk2Xnt4z3f49ovcnfrv6NPMGH/LYImAoVtEbN+6Vi5PJpMjQ==}
    peerDependencies:
      react: '>=16.8.0'

  '@huse/transition-state@1.1.1':
    resolution: {integrity: sha512-7bE0bXmX2uJWpY0Ihlq+1G5s3FL76xZtzkJ/hEVbb89+fjgsBCQIKY4lU9y72rcai6Qf54KmHHG2A8Zvog58tQ==}
    peerDependencies:
      react: '>=16.8.0'

  '@huse/update@1.1.1':
    resolution: {integrity: sha512-AeIKE1UJjIbH21sALRmbjmBLwUxfgriqI093+Z8j0XTomo8uJh/czlUsN7G/cwzM6GpB/9brizOAOjo9N6HNBw==}
    peerDependencies:
      react: '>=16.8.0'

  '@huse/user-media@0.10.1':
    resolution: {integrity: sha512-dBGtneWtbb4seOcvEqR/b8O0qPbpTvTnYdi7GSi01NjH72NxyV6NuKlAmIIXWXgk/z5hkh4a2mktUPBAdtFPYQ==}
    peerDependencies:
      react: '>=16.8.0'

  '@huse/web-socket@0.11.1':
    resolution: {integrity: sha512-509xO5G+4ZwueSenfYCX7IwKTLaj+EBxR1y2sd7YWoFfdhfA+amD0bbUL/DJxCR6oLfs0C6WpAnCvCRZ58Bpxg==}
    peerDependencies:
      react: '>=16.8.0'

  '@huse/window-size@1.1.1':
    resolution: {integrity: sha512-Mp8W2AOZ+uAOP/aroz++F1b5RUk+TdF3p33O+XB/0Jn5jxIe6X8XltGCXP50l4clSFjqBBzKROVfYvkA/O1Zig==}
    peerDependencies:
      react: '>=16.8.0'

  '@jridgewell/gen-mapping@0.3.13':
    resolution: {integrity: sha512-2kkt/7niJ6MgEPxF0bYdQ6etZaA+fQvDcLKckhy1yIQOzaoKjBBjSj63/aLVjYE3qhRt5dvM+uUyfCg6UKCBbA==}

  '@jridgewell/resolve-uri@3.1.2':
    resolution: {integrity: sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==}
    engines: {node: '>=6.0.0'}

  '@jridgewell/sourcemap-codec@1.5.5':
    resolution: {integrity: sha512-cYQ9310grqxueWbl+WuIUIaiUaDcj7WOq5fVhEljNVgRfOUhY9fy2zTvfoqWsnebh8Sl70VScFbICvJnLKB0Og==}

  '@jridgewell/trace-mapping@0.3.30':
    resolution: {integrity: sha512-GQ7Nw5G2lTu/BtHTKfXhKHok2WGetd4XYcVKGx00SjAk8GMwgJM3zr6zORiPGuOE+/vkc90KtTosSSvaCjKb2Q==}

  '@napi-rs/wasm-runtime@1.0.5':
    resolution: {integrity: sha512-TBr9Cf9onSAS2LQ2+QHx6XcC6h9+RIzJgbqG3++9TUZSH204AwEy5jg3BTQ0VATsyoGj4ee49tN/y6rvaOOtcg==}

  '@nodelib/fs.scandir@2.1.5':
    resolution: {integrity: sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==}
    engines: {node: '>= 8'}

  '@nodelib/fs.stat@2.0.5':
    resolution: {integrity: sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==}
    engines: {node: '>= 8'}

  '@nodelib/fs.walk@1.2.8':
    resolution: {integrity: sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==}
    engines: {node: '>= 8'}

  '@oxc-minify/binding-android-arm64@0.89.0':
    resolution: {integrity: sha512-CkiQLbYYeazpaTRQr8ZBLtuh7pw7QUDcoDcdem48Kfb8B9Oaau+MhgG+aeCnewsb2r7vg65vzZ95voTkL8ejAg==}
    engines: {node: '>=14.0.0'}
    cpu: [arm64]
    os: [android]

  '@oxc-minify/binding-darwin-arm64@0.89.0':
    resolution: {integrity: sha512-fizT3ZyX9fQE8MI9nvz1gRLrXCG3v0BZCaLfzls4nigCpbaIJ9Q+EpZOhVww6bkyzmZdq7qdcRNuqd2qnfveEg==}
    engines: {node: '>=14.0.0'}
    cpu: [arm64]
    os: [darwin]

  '@oxc-minify/binding-darwin-x64@0.89.0':
    resolution: {integrity: sha512-bhYCV8r8V/S8sGqv9QuJg2caFwXKpD8VbsE017uyD7z5XhVbjLcXwkMDGC+f2pLbsQuKerPSSfbdGs4trZ0qRg==}
    engines: {node: '>=14.0.0'}
    cpu: [x64]
    os: [darwin]

  '@oxc-minify/binding-freebsd-x64@0.89.0':
    resolution: {integrity: sha512-VdhOimpYd9ypLOBuC9F0SEqUjeGap1jY0KulfEaSt4OWqooQLoZgOtEgC6zXeYELhURZ3vNX7/ngEaT/3olsaA==}
    engines: {node: '>=14.0.0'}
    cpu: [x64]
    os: [freebsd]

  '@oxc-minify/binding-linux-arm-gnueabihf@0.89.0':
    resolution: {integrity: sha512-oeRiPNqdkJx1R8ncogY5DmCAzO5r4g0BWHB9xo7Qn84PKpzwwrSwBrKrRELUNAmBqIB4c7ifrs/LSTyt/LSd4g==}
    engines: {node: '>=14.0.0'}
    cpu: [arm]
    os: [linux]

  '@oxc-minify/binding-linux-arm-musleabihf@0.89.0':
    resolution: {integrity: sha512-JZg0MBNTaoFC0culAPdZLNKx/U5sSeMUSwz2hFJ+jbC+bBqJF9eMy/Oi9stP+66WDnoifX/KQVIIzw6/EHBYvg==}
    engines: {node: '>=14.0.0'}
    cpu: [arm]
    os: [linux]

  '@oxc-minify/binding-linux-arm64-gnu@0.89.0':
    resolution: {integrity: sha512-2inxGU/RN2pZNrJ/7rjyqor1MGEDWiww55J3yVC37bGBBuf8Hk+3MR4jVWib7KM3qlNlnUUeHBUW1/bT9LpzWg==}
    engines: {node: '>=14.0.0'}
    cpu: [arm64]
    os: [linux]

  '@oxc-minify/binding-linux-arm64-musl@0.89.0':
    resolution: {integrity: sha512-knXbeP7P+t9SvfyC3SVDlYr4YjFx4IDd/g/AGTMOEz0aBWxVlMsfiY1b9x0ZowSrz63K6dDSfZBZ/T5CVCCMxQ==}
    engines: {node: '>=14.0.0'}
    cpu: [arm64]
    os: [linux]

  '@oxc-minify/binding-linux-riscv64-gnu@0.89.0':
    resolution: {integrity: sha512-y2+p0Q3f1/N5KKfx0IfzjxeJ2ehZ7+RScE+90XfoRMWIi+4K7MKZrs7XFXChPPw1gQ8KuElY3/UiLw7R9pHaxg==}
    engines: {node: '>=14.0.0'}
    cpu: [riscv64]
    os: [linux]

  '@oxc-minify/binding-linux-s390x-gnu@0.89.0':
    resolution: {integrity: sha512-aEVqCS8lKL4+vs0oRwsA6TKDcKHqH766IAqzPfzqkPJajDImTZhdKKghJye5SumzKFW24X4A2B/Y1pqgsHOE0Q==}
    engines: {node: '>=14.0.0'}
    cpu: [s390x]
    os: [linux]

  '@oxc-minify/binding-linux-x64-gnu@0.89.0':
    resolution: {integrity: sha512-9vJzQPxskFs8Fqn+lOVf17alVWAbVEUT8Ihohxy+/+YXeVj1mqpxVoHyP3S71n6ZiP9diOPYgdXpCKoyJTKmRw==}
    engines: {node: '>=14.0.0'}
    cpu: [x64]
    os: [linux]

  '@oxc-minify/binding-linux-x64-musl@0.89.0':
    resolution: {integrity: sha512-jOAfzDtu8spXPy3SOGTuqF4u7MKRrH+wwaWcCRVb7rU0WgW/CSJ8eMXJ+rWNfznoAq/ph8xY7SziXAMvWJmpww==}
    engines: {node: '>=14.0.0'}
    cpu: [x64]
    os: [linux]

  '@oxc-minify/binding-wasm32-wasi@0.89.0':
    resolution: {integrity: sha512-+JOh0A+T7CIa1V2dnW5pxh3uixyqlvd7WuFhi46aQYSc4shls8DHZlBeWI3s1VyVOMdV96ZpaovXdjzBoJq7Ug==}
    engines: {node: '>=14.0.0'}
    cpu: [wasm32]

  '@oxc-minify/binding-win32-arm64-msvc@0.89.0':
    resolution: {integrity: sha512-zgq0XVhWPTbR4rTdQyalROIUgqaUYniBV2FhLYpxaBPXdvOzc7vouXZTJOGkLt46HdsOpfkAPIceJcFMwfotJA==}
    engines: {node: '>=14.0.0'}
    cpu: [arm64]
    os: [win32]

  '@oxc-minify/binding-win32-x64-msvc@0.89.0':
    resolution: {integrity: sha512-m1hbBJpK7SfXF8rqnLRx2Wp9qWeFbweWDlAsbhHogTW5JwD82Zll3oi4gICGu+omtYdhPP6Lt7zWSR2FXZKxMA==}
    engines: {node: '>=14.0.0'}
    cpu: [x64]
    os: [win32]

  '@oxc-parser/binding-android-arm64@0.89.0':
    resolution: {integrity: sha512-Tz0FoCIU/MGteWnCLk8UjN6EgNCnFO/HPrf6/Slg2snFYBaEv4kQVSfe5uN6DWlhuX0/Hk891bJif7kvQkXskg==}
    engines: {node: '>=20.0.0'}
    cpu: [arm64]
    os: [android]

  '@oxc-parser/binding-darwin-arm64@0.89.0':
    resolution: {integrity: sha512-kypXATIbctk9djDWXuC7XGbfOgiNJgYpIHIN7lyIhEqYvvrpmSiRaufn2zIjzDxmJFyok4A7wHJH7LsNwrOZhw==}
    engines: {node: '>=20.0.0'}
    cpu: [arm64]
    os: [darwin]

  '@oxc-parser/binding-darwin-x64@0.89.0':
    resolution: {integrity: sha512-0mBLPC7LH6FCTfpq8vjZ12ZevOPN8XvrxV6zIyUlW2Xv/s4AJ5myn1Vqy3g0kCJYVoyTNWOLHzkTp2uDx0VQOw==}
    engines: {node: '>=20.0.0'}
    cpu: [x64]
    os: [darwin]

  '@oxc-parser/binding-freebsd-x64@0.89.0':
    resolution: {integrity: sha512-UWQai6zd3+w+Z7iRHkw8ZD5dwrlAWkbsBmK7nfNHwOa9gew/J10iS7aatX0YQ8MzpUVa5wIofGmB4kOoG/IzFg==}
    engines: {node: '>=20.0.0'}
    cpu: [x64]
    os: [freebsd]

  '@oxc-parser/binding-linux-arm-gnueabihf@0.89.0':
    resolution: {integrity: sha512-vfi2bef+VMp0bcPomQugQcpEe4GpYKhT2HFCgUplEMneQNuPj5z/rF6u1Ix0y13MisO8cbNhxP0oB1tvqmPA6Q==}
    engines: {node: '>=20.0.0'}
    cpu: [arm]
    os: [linux]

  '@oxc-parser/binding-linux-arm-musleabihf@0.89.0':
    resolution: {integrity: sha512-EvLiLQSYkBTtp8c91bjBCxlf0rMIq3jfcYopEx5jz9lv/nhJGrxkzVvj0kAN3f2iufbkk0PygKkSvQx0Kpy9zQ==}
    engines: {node: '>=20.0.0'}
    cpu: [arm]
    os: [linux]

  '@oxc-parser/binding-linux-arm64-gnu@0.89.0':
    resolution: {integrity: sha512-jeI7qoHvvUIqLCN4FAPmTLpcQrlL3cuteH0HuYBmeVdBcJyPLAWAht7EaDlPjYOFvp4e9Tp3IqrpdqCztCWdAw==}
    engines: {node: '>=20.0.0'}
    cpu: [arm64]
    os: [linux]

  '@oxc-parser/binding-linux-arm64-musl@0.89.0':
    resolution: {integrity: sha512-Pxe89cKhKNvHm6eii1zWr711sEyxFzJxINYAP5SM7PgajQb9dZq8Le0e68NYADlsoXVc/zcYlo+aC2yhjxNMVA==}
    engines: {node: '>=20.0.0'}
    cpu: [arm64]
    os: [linux]

  '@oxc-parser/binding-linux-riscv64-gnu@0.89.0':
    resolution: {integrity: sha512-g9R9Uph7gi9SBYKop5SdNK3c9WCBZSeYkE3IU3dF5DZnR/xqBkVRKnc+LAuboijEhhqLw4XwbQ49Wd6cBDjSTg==}
    engines: {node: '>=20.0.0'}
    cpu: [riscv64]
    os: [linux]

  '@oxc-parser/binding-linux-s390x-gnu@0.89.0':
    resolution: {integrity: sha512-RFalRHelX4EFJcp3Byy4z5ZnYoEFSlRmgZECYfr/7yPSmo79DKL2VEHeVzIAKFbfTOPDIm0yw9BbUEBDritWDQ==}
    engines: {node: '>=20.0.0'}
    cpu: [s390x]
    os: [linux]

  '@oxc-parser/binding-linux-x64-gnu@0.89.0':
    resolution: {integrity: sha512-BjU07UxFMSm0/7c79KBBCC2Meef45CQ7dg068xLK6voZgEY24NNq2F0xfl7FxBrymR/viluSP5gopQYjHXqc4A==}
    engines: {node: '>=20.0.0'}
    cpu: [x64]
    os: [linux]

  '@oxc-parser/binding-linux-x64-musl@0.89.0':
    resolution: {integrity: sha512-RLecF47V/uy3voAt2BFywfh1RUtHlgdz/oG9oMG0E+A/3ZQPTEB1WuJyAnDsC0anxQS5W2J+/az3l5sL4/n54w==}
    engines: {node: '>=20.0.0'}
    cpu: [x64]
    os: [linux]

  '@oxc-parser/binding-wasm32-wasi@0.89.0':
    resolution: {integrity: sha512-h9uyojS96as3+KnvQ6MojTBHeHTpN88ejzoNxEcUbhgupIYTCp59G+5BQh7E+w4lrCuGmnjphFkca07RvWQpbQ==}
    engines: {node: '>=14.0.0'}
    cpu: [wasm32]

  '@oxc-parser/binding-win32-arm64-msvc@0.89.0':
    resolution: {integrity: sha512-OGa7mETyx7Mg0ecLQraawzRrVQDinNP7WQQ7CjDD/Y1YgKM/8oN9xa5TaVe5dh66pbKOz49NJ4LccaUs58UaMQ==}
    engines: {node: '>=20.0.0'}
    cpu: [arm64]
    os: [win32]

  '@oxc-parser/binding-win32-x64-msvc@0.89.0':
    resolution: {integrity: sha512-uNRWb1VNW/OiJf26rc6XY63kRoHmIXMpsKFjECTGIfaxwNDKx/mtJSvLQaFDZxoWuY9VsNrtOqNidgkYeW3tfQ==}
    engines: {node: '>=20.0.0'}
    cpu: [x64]
    os: [win32]

  '@oxc-project/runtime@0.78.0':
    resolution: {integrity: sha512-jOU7sDFMyq5ShGJC21UobalVzqcdtWGfySVp8ELvKoVLzMpLHb4kv1bs9VKxaP8XC7Z9hlAXwEKVhCTN+j21aQ==}
    engines: {node: '>=6.9.0'}

  '@oxc-project/types@0.78.0':
    resolution: {integrity: sha512-8FvExh0WRWN1FoSTjah1xa9RlavZcJQ8/yxRbZ7ElmSa2Ij5f5Em7MvRbSthE6FbwC6Wh8iAw0Gpna7QdoqLGg==}

  '@oxc-project/types@0.89.0':
    resolution: {integrity: sha512-yuo+ECPIW5Q9mSeNmCDC2im33bfKuwW18mwkaHMQh8KakHYDzj4ci/q7wxf2qS3dMlVVCIyrs3kFtH5LmnlYnw==}

  '@oxc-transform/binding-android-arm64@0.89.0':
    resolution: {integrity: sha512-ClAT3Uwuo++nxwJxKpxiav3jKIRzMSc0mHBoEvBFQgY4rjjwNe13N6ktFwJH6paslQNfJ8NhrzbCgUcj9sFpvQ==}
    engines: {node: '>=14.0.0'}
    cpu: [arm64]
    os: [android]

  '@oxc-transform/binding-darwin-arm64@0.89.0':
    resolution: {integrity: sha512-p8ZPY/9A/sbxEZnpFhpFXcmBKESyeGsM+W0uHcP/y2q+esy7aXx4UZlT64zdxqgtepPK7e2LFYYS4RdJTLdM0w==}
    engines: {node: '>=14.0.0'}
    cpu: [arm64]
    os: [darwin]

  '@oxc-transform/binding-darwin-x64@0.89.0':
    resolution: {integrity: sha512-GvR99rtmk90Jk9HjxVEHrtndldVaQ2FxJaESzg/zaQaCAN50R1WR8mzfIQN5Pdi5ZFM3dUgxVhVbWmSc6SbpmQ==}
    engines: {node: '>=14.0.0'}
    cpu: [x64]
    os: [darwin]

  '@oxc-transform/binding-freebsd-x64@0.89.0':
    resolution: {integrity: sha512-wCNAlWW6ylqQxFFwcOkrQ77HeVaxrsHlKH+POXB1ercbb+OXARhAQaZG7crGGad8RTn1HwkudAl4W7XwcwW8ng==}
    engines: {node: '>=14.0.0'}
    cpu: [x64]
    os: [freebsd]

  '@oxc-transform/binding-linux-arm-gnueabihf@0.89.0':
    resolution: {integrity: sha512-7nfOV49v/NsKkcRaMf0RXtnTtYZV+DXgptaBdkGBrqJAtUQ6uvDERJLzRMMiHKfl8qXpvguMjLwCPELDMtLaqA==}
    engines: {node: '>=14.0.0'}
    cpu: [arm]
    os: [linux]

  '@oxc-transform/binding-linux-arm-musleabihf@0.89.0':
    resolution: {integrity: sha512-lOSBrPpTTpF2Jr3in2JL4WymKm8V5hPnniVTwhZcpecaBBKmK+qYW88FJJuHkO3uZYx7ZwfkEb0FN7S81hQ2Fw==}
    engines: {node: '>=14.0.0'}
    cpu: [arm]
    os: [linux]

  '@oxc-transform/binding-linux-arm64-gnu@0.89.0':
    resolution: {integrity: sha512-fDI14uxb+sm0lcbrRdtm5cvitxNwQ+271hMVqeMm5gUkDCbY0agGvdvbEq/hBuMaMh7BL2uU9fmxOmdRgfmAqg==}
    engines: {node: '>=14.0.0'}
    cpu: [arm64]
    os: [linux]

  '@oxc-transform/binding-linux-arm64-musl@0.89.0':
    resolution: {integrity: sha512-9OACjq3oRywMaSW5SQDz6Y2AS62EQ0nkLor7P2YSdnte1LbrciwfqMYm8v6HRJ8Pqif2iqtAc9YlU8OtefSvkA==}
    engines: {node: '>=14.0.0'}
    cpu: [arm64]
    os: [linux]

  '@oxc-transform/binding-linux-riscv64-gnu@0.89.0':
    resolution: {integrity: sha512-or2lJ/Uhjaj/Hbz3f741cZM+FKZNwKL20wELOro0xraL7NwThHTHEZGQrSHlJ/VkRdJPuo+UC3xB9OgdY6vvFw==}
    engines: {node: '>=14.0.0'}
    cpu: [riscv64]
    os: [linux]

  '@oxc-transform/binding-linux-s390x-gnu@0.89.0':
    resolution: {integrity: sha512-HdC3Z3CTveXgMu13wF6qqjpnH7/Ds9BeOEYU5xGPXAFkTLMxL6miYiLN+kgTekTZeYPphM3Dnx/+Fwrmi1ukDw==}
    engines: {node: '>=14.0.0'}
    cpu: [s390x]
    os: [linux]

  '@oxc-transform/binding-linux-x64-gnu@0.89.0':
    resolution: {integrity: sha512-w5xtFpVyUEPo0/gLInZ+NbP5pT6+CG1ukoyZYr3Y+J8cz+xezNokzjeAu4p7MmG6XG63DKBG60dlBVL1HCgtWA==}
    engines: {node: '>=14.0.0'}
    cpu: [x64]
    os: [linux]

  '@oxc-transform/binding-linux-x64-musl@0.89.0':
    resolution: {integrity: sha512-MxtHJp0HwH1F6gfAnAtr8W9gD03Hnj9DIlaFcqCXYCEvvzpxo/8kG5E3Y2UCOfR1h2rV2haX4k7+o5Swr+/dMw==}
    engines: {node: '>=14.0.0'}
    cpu: [x64]
    os: [linux]

  '@oxc-transform/binding-wasm32-wasi@0.89.0':
    resolution: {integrity: sha512-YPkhOluELfgPgY02RqGwc0Q17HGVNSw3bHJ60pc4IppXAbigXwX+IpWYYowcMtWOr6zAawUAxakUEeLb5yv3pg==}
    engines: {node: '>=14.0.0'}
    cpu: [wasm32]

  '@oxc-transform/binding-win32-arm64-msvc@0.89.0':
    resolution: {integrity: sha512-gsuSbYzBcqfKx7X7c1rZJGskmXDlLMkwm4JmhCVBG91L14j+9eFIOdXk3U6E5/OtiFMtr4WIDlSApEfhDh78Rw==}
    engines: {node: '>=14.0.0'}
    cpu: [arm64]
    os: [win32]

  '@oxc-transform/binding-win32-x64-msvc@0.89.0':
    resolution: {integrity: sha512-5HYV5lUVQ/3NJw4QVtgCaPy+6us+YlVxKWE934ulGGlnwZvTgMvsSXiwNdJwkgWhLcfv7Y1NtkFvcMDBi1Gpzg==}
    engines: {node: '>=14.0.0'}
    cpu: [x64]
    os: [win32]

  '@rolldown/binding-android-arm64@1.0.0-beta.30':
    resolution: {integrity: sha512-4j7QBitb/WMT1fzdJo7BsFvVNaFR5WCQPdf/RPDHEsgQIYwBaHaL47KTZxncGFQDD1UAKN3XScJ0k7LAsZfsvg==}
    cpu: [arm64]
    os: [android]

  '@rolldown/binding-darwin-arm64@1.0.0-beta.30':
    resolution: {integrity: sha512-4vWFTe1o5LXeitI2lW8qMGRxxwrH/LhKd2HDLa/QPhdxohvdnfKyDZWN96XUhDyje2bHFCFyhMs3ak2lg2mJFA==}
    cpu: [arm64]
    os: [darwin]

  '@rolldown/binding-darwin-x64@1.0.0-beta.30':
    resolution: {integrity: sha512-MxrfodqImbsDFFFU/8LxyFPZjt7s4ht8g2Zb76EmIQ+xlmit46L9IzvWiuMpEaSJ5WbnjO7fCDWwakMGyJJ+Dw==}
    cpu: [x64]
    os: [darwin]

  '@rolldown/binding-freebsd-x64@1.0.0-beta.30':
    resolution: {integrity: sha512-c/TQXcATKoO8qE1bCjCOkymZTu7yVUAxBSNLp42Q97XHCb0Cu9v6MjZpB6c7Hq9NQ9NzW44uglak9D/r77JeDw==}
    cpu: [x64]
    os: [freebsd]

  '@rolldown/binding-linux-arm-gnueabihf@1.0.0-beta.30':
    resolution: {integrity: sha512-Vxci4xylM11zVqvrmezAaRjGBDyOlMRtlt7TDgxaBmSYLuiokXbZpD8aoSuOyjUAeN0/tmWItkxNGQza8UWGNQ==}
    cpu: [arm]
    os: [linux]

  '@rolldown/binding-linux-arm64-gnu@1.0.0-beta.30':
    resolution: {integrity: sha512-iEBEdSs25Ol0lXyVNs763f7YPAIP0t1EAjoXME81oJ94DesJslaLTj71Rn1shoMDVA+dfkYA286w5uYnOs9ZNA==}
    cpu: [arm64]
    os: [linux]

  '@rolldown/binding-linux-arm64-musl@1.0.0-beta.30':
    resolution: {integrity: sha512-Ny684Sn1X8c+gGLuDlxkOuwiEE3C7eEOqp1/YVBzQB4HO7U/b4n7alvHvShboOEY5DP1fFUjq6Z+sBLYlCIZbQ==}
    cpu: [arm64]
    os: [linux]

  '@rolldown/binding-linux-arm64-ohos@1.0.0-beta.30':
    resolution: {integrity: sha512-6moyULHDPKwt5RDEV72EqYw5n+s46AerTwtEBau5wCsZd1wuHS1L9z6wqhKISXAFTK9sneN0TEjvYKo+sgbbiA==}
    cpu: [arm64]
    os: [openharmony]

  '@rolldown/binding-linux-x64-gnu@1.0.0-beta.30':
    resolution: {integrity: sha512-p0yoPdoGg5Ow2YZKKB5Ypbn58i7u4XFk3PvMkriFnEcgtVk40c5u7miaX7jH0JdzahyXVBJ/KT5yEpJrzQn8yg==}
    cpu: [x64]
    os: [linux]

  '@rolldown/binding-linux-x64-musl@1.0.0-beta.30':
    resolution: {integrity: sha512-sM/KhCrsT0YdHX10mFSr0cvbfk1+btG6ftepAfqhbcDfhi0s65J4dTOxGmklJnJL9i1LXZ8WA3N4wmnqsfoK8Q==}
    cpu: [x64]
    os: [linux]

  '@rolldown/binding-wasm32-wasi@1.0.0-beta.30':
    resolution: {integrity: sha512-i3kD5OWs8PQP0V+JW3TFyCLuyjuNzrB45em0g84Jc+gvnDsGVlzVjMNPo7txE/yT8CfE90HC/lDs3ry9FvaUyw==}
    engines: {node: '>=14.0.0'}
    cpu: [wasm32]

  '@rolldown/binding-win32-arm64-msvc@1.0.0-beta.30':
    resolution: {integrity: sha512-q7mrYln30V35VrCqnBVQQvNPQm8Om9HC59I3kMYiOWogvJobzSPyO+HA1MP363+Qgwe39I2I1nqBKPOtWZ33AQ==}
    cpu: [arm64]
    os: [win32]

  '@rolldown/binding-win32-ia32-msvc@1.0.0-beta.30':
    resolution: {integrity: sha512-nUqGBt39XTpbBEREEnyKofdP3uz+SN/x2884BH+N3B2NjSUrP6NXwzltM35C0wKK42hX/nthRrwSgj715m99Jw==}
    cpu: [ia32]
    os: [win32]

  '@rolldown/binding-win32-x64-msvc@1.0.0-beta.30':
    resolution: {integrity: sha512-lbnvUwAXIVWSXAeZrCa4b1KvV/DW0rBnMHuX0T7I6ey1IsXZ90J37dEgt3j48Ex1Cw1E+5H7VDNP2gyOX8iu3w==}
    cpu: [x64]
    os: [win32]

  '@rolldown/pluginutils@1.0.0-beta.30':
    resolution: {integrity: sha512-whXaSoNUFiyDAjkUF8OBpOm77Szdbk5lGNqFe6CbVbJFrhCCPinCbRA3NjawwlNHla1No7xvXXh+CpSxnPfUEw==}

  '@rolldown/pluginutils@1.0.0-beta.32':
    resolution: {integrity: sha512-QReCdvxiUZAPkvp1xpAg62IeNzykOFA6syH2CnClif4YmALN1XKpB39XneL80008UbtMShthSVDKmrx05N1q/g==}

  '@rollup/rollup-android-arm-eabi@4.49.0':
    resolution: {integrity: sha512-rlKIeL854Ed0e09QGYFlmDNbka6I3EQFw7iZuugQjMb11KMpJCLPFL4ZPbMfaEhLADEL1yx0oujGkBQ7+qW3eA==}
    cpu: [arm]
    os: [android]

  '@rollup/rollup-android-arm64@4.49.0':
    resolution: {integrity: sha512-cqPpZdKUSQYRtLLr6R4X3sD4jCBO1zUmeo3qrWBCqYIeH8Q3KRL4F3V7XJ2Rm8/RJOQBZuqzQGWPjjvFUcYa/w==}
    cpu: [arm64]
    os: [android]

  '@rollup/rollup-darwin-arm64@4.49.0':
    resolution: {integrity: sha512-99kMMSMQT7got6iYX3yyIiJfFndpojBmkHfTc1rIje8VbjhmqBXE+nb7ZZP3A5skLyujvT0eIUCUsxAe6NjWbw==}
    cpu: [arm64]
    os: [darwin]

  '@rollup/rollup-darwin-x64@4.49.0':
    resolution: {integrity: sha512-y8cXoD3wdWUDpjOLMKLx6l+NFz3NlkWKcBCBfttUn+VGSfgsQ5o/yDUGtzE9HvsodkP0+16N0P4Ty1VuhtRUGg==}
    cpu: [x64]
    os: [darwin]

  '@rollup/rollup-freebsd-arm64@4.49.0':
    resolution: {integrity: sha512-3mY5Pr7qv4GS4ZvWoSP8zha8YoiqrU+e0ViPvB549jvliBbdNLrg2ywPGkgLC3cmvN8ya3za+Q2xVyT6z+vZqA==}
    cpu: [arm64]
    os: [freebsd]

  '@rollup/rollup-freebsd-x64@4.49.0':
    resolution: {integrity: sha512-C9KzzOAQU5gU4kG8DTk+tjdKjpWhVWd5uVkinCwwFub2m7cDYLOdtXoMrExfeBmeRy9kBQMkiyJ+HULyF1yj9w==}
    cpu: [x64]
    os: [freebsd]

  '@rollup/rollup-linux-arm-gnueabihf@4.49.0':
    resolution: {integrity: sha512-OVSQgEZDVLnTbMq5NBs6xkmz3AADByCWI4RdKSFNlDsYXdFtlxS59J+w+LippJe8KcmeSSM3ba+GlsM9+WwC1w==}
    cpu: [arm]
    os: [linux]

  '@rollup/rollup-linux-arm-musleabihf@4.49.0':
    resolution: {integrity: sha512-ZnfSFA7fDUHNa4P3VwAcfaBLakCbYaxCk0jUnS3dTou9P95kwoOLAMlT3WmEJDBCSrOEFFV0Y1HXiwfLYJuLlA==}
    cpu: [arm]
    os: [linux]

  '@rollup/rollup-linux-arm64-gnu@4.49.0':
    resolution: {integrity: sha512-Z81u+gfrobVK2iV7GqZCBfEB1y6+I61AH466lNK+xy1jfqFLiQ9Qv716WUM5fxFrYxwC7ziVdZRU9qvGHkYIJg==}
    cpu: [arm64]
    os: [linux]

  '@rollup/rollup-linux-arm64-musl@4.49.0':
    resolution: {integrity: sha512-zoAwS0KCXSnTp9NH/h9aamBAIve0DXeYpll85shf9NJ0URjSTzzS+Z9evmolN+ICfD3v8skKUPyk2PO0uGdFqg==}
    cpu: [arm64]
    os: [linux]

  '@rollup/rollup-linux-loongarch64-gnu@4.49.0':
    resolution: {integrity: sha512-2QyUyQQ1ZtwZGiq0nvODL+vLJBtciItC3/5cYN8ncDQcv5avrt2MbKt1XU/vFAJlLta5KujqyHdYtdag4YEjYQ==}
    cpu: [loong64]
    os: [linux]

  '@rollup/rollup-linux-ppc64-gnu@4.49.0':
    resolution: {integrity: sha512-k9aEmOWt+mrMuD3skjVJSSxHckJp+SiFzFG+v8JLXbc/xi9hv2icSkR3U7uQzqy+/QbbYY7iNB9eDTwrELo14g==}
    cpu: [ppc64]
    os: [linux]

  '@rollup/rollup-linux-riscv64-gnu@4.49.0':
    resolution: {integrity: sha512-rDKRFFIWJ/zJn6uk2IdYLc09Z7zkE5IFIOWqpuU0o6ZpHcdniAyWkwSUWE/Z25N/wNDmFHHMzin84qW7Wzkjsw==}
    cpu: [riscv64]
    os: [linux]

  '@rollup/rollup-linux-riscv64-musl@4.49.0':
    resolution: {integrity: sha512-FkkhIY/hYFVnOzz1WeV3S9Bd1h0hda/gRqvZCMpHWDHdiIHn6pqsY3b5eSbvGccWHMQ1uUzgZTKS4oGpykf8Tw==}
    cpu: [riscv64]
    os: [linux]

  '@rollup/rollup-linux-s390x-gnu@4.49.0':
    resolution: {integrity: sha512-gRf5c+A7QiOG3UwLyOOtyJMD31JJhMjBvpfhAitPAoqZFcOeK3Kc1Veg1z/trmt+2P6F/biT02fU19GGTS529A==}
    cpu: [s390x]
    os: [linux]

  '@rollup/rollup-linux-x64-gnu@4.49.0':
    resolution: {integrity: sha512-BR7+blScdLW1h/2hB/2oXM+dhTmpW3rQt1DeSiCP9mc2NMMkqVgjIN3DDsNpKmezffGC9R8XKVOLmBkRUcK/sA==}
    cpu: [x64]
    os: [linux]

  '@rollup/rollup-linux-x64-musl@4.49.0':
    resolution: {integrity: sha512-hDMOAe+6nX3V5ei1I7Au3wcr9h3ktKzDvF2ne5ovX8RZiAHEtX1A5SNNk4zt1Qt77CmnbqT+upb/umzoPMWiPg==}
    cpu: [x64]
    os: [linux]

  '@rollup/rollup-win32-arm64-msvc@4.49.0':
    resolution: {integrity: sha512-wkNRzfiIGaElC9kXUT+HLx17z7D0jl+9tGYRKwd8r7cUqTL7GYAvgUY++U2hK6Ar7z5Z6IRRoWC8kQxpmM7TDA==}
    cpu: [arm64]
    os: [win32]

  '@rollup/rollup-win32-ia32-msvc@4.49.0':
    resolution: {integrity: sha512-gq5aW/SyNpjp71AAzroH37DtINDcX1Qw2iv9Chyz49ZgdOP3NV8QCyKZUrGsYX9Yyggj5soFiRCgsL3HwD8TdA==}
    cpu: [ia32]
    os: [win32]

  '@rollup/rollup-win32-x64-msvc@4.49.0':
    resolution: {integrity: sha512-gEtqFbzmZLFk2xKh7g0Rlo8xzho8KrEFEkzvHbfUGkrgXOpZ4XagQ6n+wIZFNh1nTb8UD16J4nFSFKXYgnbdBg==}
    cpu: [x64]
    os: [win32]

  '@tybys/wasm-util@0.10.1':
    resolution: {integrity: sha512-9tTaPJLSiejZKx+Bmog4uSubteqTvFrVrURwkmHixBo0G4seD0zUxp98E1DzUBJxLQ3NPwXrGKDiVjwx/DpPsg==}

  '@types/babel__core@7.20.5':
    resolution: {integrity: sha512-qoQprZvz5wQFJwMDqeseRXWv3rqMvhgpbXFfVyWhbx9X47POIA6i/+dXefEmZKoAgOaTdaIgNSMqMIU61yRyzA==}

  '@types/babel__generator@7.27.0':
    resolution: {integrity: sha512-ufFd2Xi92OAVPYsy+P4n7/U7e68fex0+Ee8gSG9KX7eo084CWiQ4sdxktvdl0bOPupXtVJPY19zk6EwWqUQ8lg==}

  '@types/babel__template@7.4.4':
    resolution: {integrity: sha512-h/NUaSyG5EyxBIp8YRxo4RMe2/qQgvyowRwVMzhYhBCONbW8PUsg4lkFMrhgZhUe5z3L3MiLDuvyJ/CaPa2A8A==}

  '@types/babel__traverse@7.28.0':
    resolution: {integrity: sha512-8PvcXf70gTDZBgt9ptxJ8elBeBjcLOAcOtoO/mPJjtji1+CdGbHgm77om1GrsPxsiE+uXIpNSK64UYaIwQXd4Q==}

  '@types/estree@1.0.8':
    resolution: {integrity: sha512-dWHzHa2WqEXI/O1E9OjrocMTKJl2mSrEolh1Iomrv6U+JuNwaHXsXx9bLu5gG7BUWFIN0skIQJQ/L1rIex4X6w==}

  '@types/json-schema@7.0.15':
    resolution: {integrity: sha512-5+fP8P8MFNC+AyZCDxrB2pkZFPGzqQWUzpSeuuVLvm8VMcorNYavBqoFcxK8bQz4Qsbn4oUEEem4wDLfcysGHA==}

  '@types/node@18.19.123':
    resolution: {integrity: sha512-K7DIaHnh0mzVxreCR9qwgNxp3MH9dltPNIEddW9MYUlcKAzm+3grKNSTe2vCJHI1FaLpvpL5JGJrz1UZDKYvDg==}

  '@types/react-dom@19.1.8':
    resolution: {integrity: sha512-xG7xaBMJCpcK0RpN8jDbAACQo54ycO6h4dSSmgv8+fu6ZIAdANkx/WsawASUjVXYfy+J9AbUpRMNNEsXCDfDBQ==}
    peerDependencies:
      '@types/react': ^19.0.0

  '@types/react@19.1.11':
    resolution: {integrity: sha512-lr3jdBw/BGj49Eps7EvqlUaoeA0xpj3pc0RoJkHpYaCHkVK7i28dKyImLQb3JVlqs3aYSXf7qYuWOW/fgZnTXQ==}

  '@typescript-eslint/eslint-plugin@8.41.0':
    resolution: {integrity: sha512-8fz6oa6wEKZrhXWro/S3n2eRJqlRcIa6SlDh59FXJ5Wp5XRZ8B9ixpJDcjadHq47hMx0u+HW6SNa6LjJQ6NLtw==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      '@typescript-eslint/parser': ^8.41.0
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <6.0.0'

  '@typescript-eslint/parser@8.41.0':
    resolution: {integrity: sha512-gTtSdWX9xiMPA/7MV9STjJOOYtWwIJIYxkQxnSV1U3xcE+mnJSH3f6zI0RYP+ew66WSlZ5ed+h0VCxsvdC1jJg==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <6.0.0'

  '@typescript-eslint/project-service@8.41.0':
    resolution: {integrity: sha512-b8V9SdGBQzQdjJ/IO3eDifGpDBJfvrNTp2QD9P2BeqWTGrRibgfgIlBSw6z3b6R7dPzg752tOs4u/7yCLxksSQ==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      typescript: '>=4.8.4 <6.0.0'

  '@typescript-eslint/scope-manager@8.41.0':
    resolution: {integrity: sha512-n6m05bXn/Cd6DZDGyrpXrELCPVaTnLdPToyhBoFkLIMznRUQUEQdSp96s/pcWSQdqOhrgR1mzJ+yItK7T+WPMQ==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@typescript-eslint/tsconfig-utils@8.41.0':
    resolution: {integrity: sha512-TDhxYFPUYRFxFhuU5hTIJk+auzM/wKvWgoNYOPcOf6i4ReYlOoYN8q1dV5kOTjNQNJgzWN3TUUQMtlLOcUgdUw==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      typescript: '>=4.8.4 <6.0.0'

  '@typescript-eslint/type-utils@8.41.0':
    resolution: {integrity: sha512-63qt1h91vg3KsjVVonFJWjgSK7pZHSQFKH6uwqxAH9bBrsyRhO6ONoKyXxyVBzG1lJnFAJcKAcxLS54N1ee1OQ==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <6.0.0'

  '@typescript-eslint/types@8.41.0':
    resolution: {integrity: sha512-9EwxsWdVqh42afLbHP90n2VdHaWU/oWgbH2P0CfcNfdKL7CuKpwMQGjwev56vWu9cSKU7FWSu6r9zck6CVfnag==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@typescript-eslint/typescript-estree@8.41.0':
    resolution: {integrity: sha512-D43UwUYJmGhuwHfY7MtNKRZMmfd8+p/eNSfFe6tH5mbVDto+VQCayeAt35rOx3Cs6wxD16DQtIKw/YXxt5E0UQ==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      typescript: '>=4.8.4 <6.0.0'

  '@typescript-eslint/utils@8.41.0':
    resolution: {integrity: sha512-udbCVstxZ5jiPIXrdH+BZWnPatjlYwJuJkDA4Tbo3WyYLh8NvB+h/bKeSZHDOFKfphsZYJQqaFtLeXEqurQn1A==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <6.0.0'

  '@typescript-eslint/visitor-keys@8.41.0':
    resolution: {integrity: sha512-+GeGMebMCy0elMNg67LRNoVnUFPIm37iu5CmHESVx56/9Jsfdpsvbv605DQ81Pi/x11IdKUsS5nzgTYbCQU9fg==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@vitejs/plugin-react@5.0.1':
    resolution: {integrity: sha512-DE4UNaBXwtVoDJ0ccBdLVjFTWL70NRuWNCxEieTI3lrq9ORB9aOCQEKstwDXBl87NvFdbqh/p7eINGyj0BthJA==}
    engines: {node: ^20.19.0 || >=22.12.0}
    peerDependencies:
      vite: ^4.2.0 || ^5.0.0 || ^6.0.0 || ^7.0.0

  acorn-jsx@5.3.2:
    resolution: {integrity: sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==}
    peerDependencies:
      acorn: ^6.0.0 || ^7.0.0 || ^8.0.0

  acorn@8.15.0:
    resolution: {integrity: sha512-NZyJarBfL7nWwIq+FDL6Zp/yHEhePMNnnJ0y3qfieCrmNvYct8uvtiV41UvlSe6apAfk0fY1FbWx+NwfmpvtTg==}
    engines: {node: '>=0.4.0'}
    hasBin: true

  ajv@6.12.6:
    resolution: {integrity: sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==}

  ansi-styles@4.3.0:
    resolution: {integrity: sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==}
    engines: {node: '>=8'}

  ansis@4.1.0:
    resolution: {integrity: sha512-BGcItUBWSMRgOCe+SVZJ+S7yTRG0eGt9cXAHev72yuGcY23hnLA7Bky5L/xLyPINoSN95geovfBkqoTlNZYa7w==}
    engines: {node: '>=14'}

  argparse@2.0.1:
    resolution: {integrity: sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==}

  ast-kit@2.1.2:
    resolution: {integrity: sha512-cl76xfBQM6pztbrFWRnxbrDm9EOqDr1BF6+qQnnDZG2Co2LjyUktkN9GTJfBAfdae+DbT2nJf2nCGAdDDN7W2g==}
    engines: {node: '>=20.18.0'}

  async@3.2.6:
    resolution: {integrity: sha512-htCUDlxyyCLMgaM3xXg0C0LW2xqfuQ6p05pCEIsXuyQ+a1koYKTuBMzRNwmybfLgvJDMd0r1LTn4+E0Ti6C2AA==}

  balanced-match@1.0.2:
    resolution: {integrity: sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==}

  basic-auth@2.0.1:
    resolution: {integrity: sha512-NF+epuEdnUYVlGuhaxbbq+dvJttwLnGY+YixlXlME5KpQ5W3CnXA5cVTneY3SPbPDRkcjMbifrwmFYcClgOZeg==}
    engines: {node: '>= 0.8'}

  birpc@2.5.0:
    resolution: {integrity: sha512-VSWO/W6nNQdyP520F1mhf+Lc2f8pjGQOtoHHm7Ze8Go1kX7akpVIrtTa0fn+HB0QJEDVacl6aO08YE0PgXfdnQ==}

  brace-expansion@1.1.12:
    resolution: {integrity: sha512-9T9UjW3r0UW5c1Q7GTwllptXwhvYmEzFhzMfZ9H7FQWt+uZePjZPjBP/W1ZEyZ1twGWom5/56TF4lPcqjnDHcg==}

  brace-expansion@2.0.2:
    resolution: {integrity: sha512-Jt0vHyM+jmUBqojB7E1NIYadt0vI0Qxjxd2TErW94wDz+E2LAm5vKMXXwg6ZZBTHPuUlDgQHKXvjGBdfcF1ZDQ==}

  braces@3.0.3:
    resolution: {integrity: sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==}
    engines: {node: '>=8'}

  browserslist@4.25.3:
    resolution: {integrity: sha512-cDGv1kkDI4/0e5yON9yM5G/0A5u8sf5TnmdX5C9qHzI9PPu++sQ9zjm1k9NiOrf3riY4OkK0zSGqfvJyJsgCBQ==}
    engines: {node: ^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7}
    hasBin: true

  bundle-name@4.1.0:
    resolution: {integrity: sha512-tjwM5exMg6BGRI+kNmTntNsvdZS1X8BFYS6tnJ2hdH0kVxM6/eVZ2xy+FqStSWvYmtfFMDLIxurorHwDKfDz5Q==}
    engines: {node: '>=18'}

  c12@3.2.0:
    resolution: {integrity: sha512-ixkEtbYafL56E6HiFuonMm1ZjoKtIo7TH68/uiEq4DAwv9NcUX2nJ95F8TrbMeNjqIkZpruo3ojXQJ+MGG5gcQ==}
    peerDependencies:
      magicast: ^0.3.5
    peerDependenciesMeta:
      magicast:
        optional: true

  call-bind-apply-helpers@1.0.2:
    resolution: {integrity: sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ==}
    engines: {node: '>= 0.4'}

  call-bound@1.0.4:
    resolution: {integrity: sha512-+ys997U96po4Kx/ABpBCqhA9EuxJaQWDQg7295H4hBphv3IZg0boBKuwYpt4YXp6MZ5AmZQnU/tyMTlRpaSejg==}
    engines: {node: '>= 0.4'}

  callsites@3.1.0:
    resolution: {integrity: sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==}
    engines: {node: '>=6'}

  caniuse-lite@1.0.30001737:
    resolution: {integrity: sha512-BiloLiXtQNrY5UyF0+1nSJLXUENuhka2pzy2Fx5pGxqavdrxSCW4U6Pn/PoG3Efspi2frRbHpBV2XsrPE6EDlw==}

  chalk@4.1.2:
    resolution: {integrity: sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==}
    engines: {node: '>=10'}

  changelogen@0.6.2:
    resolution: {integrity: sha512-QtC7+r9BxoUm+XDAwhLbz3CgU134J1ytfE3iCpLpA4KFzX2P1e6s21RrWDwUBzfx66b1Rv+6lOA2nS2btprd+A==}
    hasBin: true

  chokidar@4.0.3:
    resolution: {integrity: sha512-Qgzu8kfBvo+cA4962jnP1KkS6Dop5NS6g7R5LFYJr4b8Ub94PPQXUksCw9PvXoeXPRRddRNC5C1JQUR2SMGtnA==}
    engines: {node: '>= 14.16.0'}

  citty@0.1.6:
    resolution: {integrity: sha512-tskPPKEs8D2KPafUypv2gxwJP8h/OaJmC82QQGGDQcHvXX43xF2VDACcJVmZ0EuSxkpO9Kc4MlrA3q0+FG58AQ==}

  color-convert@2.0.1:
    resolution: {integrity: sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==}
    engines: {node: '>=7.0.0'}

  color-name@1.1.4:
    resolution: {integrity: sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==}

  concat-map@0.0.1:
    resolution: {integrity: sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==}

  confbox@0.2.2:
    resolution: {integrity: sha512-1NB+BKqhtNipMsov4xI/NnhCKp9XG9NamYp5PVm9klAT0fsrNPjaFICsCFhNhwZJKNh7zB/3q8qXz0E9oaMNtQ==}

  consola@3.4.2:
    resolution: {integrity: sha512-5IKcdX0nnYavi6G7TtOhwkYzyjfJlatbjMjuLSfE2kYT5pMDOilZ4OvMhi637CcDICTmz3wARPoyhqyX1Y+XvA==}
    engines: {node: ^14.18.0 || >=16.10.0}

  convert-gitmoji@0.1.5:
    resolution: {integrity: sha512-4wqOafJdk2tqZC++cjcbGcaJ13BZ3kwldf06PTiAQRAB76Z1KJwZNL1SaRZMi2w1FM9RYTgZ6QErS8NUl/GBmQ==}

  convert-source-map@2.0.0:
    resolution: {integrity: sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg==}

  corser@2.0.1:
    resolution: {integrity: sha512-utCYNzRSQIZNPIcGZdQc92UVJYAhtGAteCFg0yRaFm8f0P+CPtyGyHXJcGXnffjCybUCEx3FQ2G7U3/o9eIkVQ==}
    engines: {node: '>= 0.4.0'}

  cross-spawn@7.0.6:
    resolution: {integrity: sha512-uV2QOWP2nWzsy2aMp8aRibhi9dlzF5Hgh5SHaB9OiTGEyDTiJJyx0uy51QXdyWbtAHNua4XJzUKca3OzKUd3vA==}
    engines: {node: '>= 8'}

  csstype@3.1.3:
    resolution: {integrity: sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==}

  debounce@1.2.1:
    resolution: {integrity: sha512-XRRe6Glud4rd/ZGQfiV1ruXSfbvfJedlV9Y6zOlP+2K04vBYiJEte6stfFkCP03aMnY5tsipamumUjL14fofug==}

  debug@4.4.1:
    resolution: {integrity: sha512-KcKCqiftBJcZr++7ykoDIEwSa3XWowTfNPo92BYxjXiyYEVrUQh2aLyhxBCwww+heortUFxEJYcRzosstTEBYQ==}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  deep-is@0.1.4:
    resolution: {integrity: sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ==}

  default-browser-id@5.0.0:
    resolution: {integrity: sha512-A6p/pu/6fyBcA1TRz/GqWYPViplrftcW2gZC9q79ngNCKAeR/X3gcEdXQHl4KNXV+3wgIJ1CPkJQ3IHM6lcsyA==}
    engines: {node: '>=18'}

  default-browser@5.2.1:
    resolution: {integrity: sha512-WY/3TUME0x3KPYdRRxEJJvXRHV4PyPoUsxtZa78lwItwRQRHhd2U9xOscaT/YTf8uCXIAjeJOFBVEh/7FtD8Xg==}
    engines: {node: '>=18'}

  define-lazy-prop@3.0.0:
    resolution: {integrity: sha512-N+MeXYoqr3pOgn8xfyRPREN7gHakLYjhsHhWGT3fWAiL4IkAt0iDw14QiiEm2bE30c5XX5q0FtAA3CK5f9/BUg==}
    engines: {node: '>=12'}

  defu@6.1.4:
    resolution: {integrity: sha512-mEQCMmwJu317oSz8CwdIOdwf3xMif1ttiM8LTufzc3g6kR+9Pe236twL8j3IYT1F7GfRgGcW6MWxzZjLIkuHIg==}

  destr@2.0.5:
    resolution: {integrity: sha512-ugFTXCtDZunbzasqBxrK93Ik/DRYsO6S/fedkWEMKqt04xZ4csmnmwGDBAb07QWNaGMAmnTIemsYZCksjATwsA==}

  dotenv@17.2.1:
    resolution: {integrity: sha512-kQhDYKZecqnM0fCnzI5eIv5L4cAe/iRI+HqMbO/hbRdTAeXDG+M9FjipUxNfbARuEg4iHIbhnhs78BCHNbSxEQ==}
    engines: {node: '>=12'}

  dts-resolver@2.1.2:
    resolution: {integrity: sha512-xeXHBQkn2ISSXxbJWD828PFjtyg+/UrMDo7W4Ffcs7+YWCquxU8YjV1KoxuiL+eJ5pg3ll+bC6flVv61L3LKZg==}
    engines: {node: '>=20.18.0'}
    peerDependencies:
      oxc-resolver: '>=11.0.0'
    peerDependenciesMeta:
      oxc-resolver:
        optional: true

  dunder-proto@1.0.1:
    resolution: {integrity: sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==}
    engines: {node: '>= 0.4'}

  electron-to-chromium@1.5.209:
    resolution: {integrity: sha512-Xoz0uMrim9ZETCQt8UgM5FxQF9+imA7PBpokoGcZloA1uw2LeHzTlip5cb5KOAsXZLjh/moN2vReN3ZjJmjI9A==}

  es-define-property@1.0.1:
    resolution: {integrity: sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g==}
    engines: {node: '>= 0.4'}

  es-errors@1.3.0:
    resolution: {integrity: sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==}
    engines: {node: '>= 0.4'}

  es-object-atoms@1.1.1:
    resolution: {integrity: sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA==}
    engines: {node: '>= 0.4'}

  esbuild@0.25.9:
    resolution: {integrity: sha512-CRbODhYyQx3qp7ZEwzxOk4JBqmD/seJrzPa/cGjY1VtIn5E09Oi9/dB4JwctnfZ8Q8iT7rioVv5k/FNT/uf54g==}
    engines: {node: '>=18'}
    hasBin: true

  escalade@3.2.0:
    resolution: {integrity: sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==}
    engines: {node: '>=6'}

  escape-string-regexp@4.0.0:
    resolution: {integrity: sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==}
    engines: {node: '>=10'}

  eslint-plugin-react-hooks@5.2.0:
    resolution: {integrity: sha512-+f15FfK64YQwZdJNELETdn5ibXEUQmW1DZL6KXhNnc2heoy/sg9VJJeT7n8TlMWouzWqSWavFkIhHyIbIAEapg==}
    engines: {node: '>=10'}
    peerDependencies:
      eslint: ^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0-0 || ^9.0.0

  eslint-plugin-react-refresh@0.4.20:
    resolution: {integrity: sha512-XpbHQ2q5gUF8BGOX4dHe+71qoirYMhApEPZ7sfhF/dNnOF1UXnCMGZf79SFTBO7Bz5YEIT4TMieSlJBWhP9WBA==}
    peerDependencies:
      eslint: '>=8.40'

  eslint-scope@8.4.0:
    resolution: {integrity: sha512-sNXOfKCn74rt8RICKMvJS7XKV/Xk9kA7DyJr8mJik3S7Cwgy3qlkkmyS2uQB3jiJg6VNdZd/pDBJu0nvG2NlTg==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  eslint-visitor-keys@3.4.3:
    resolution: {integrity: sha512-wpc+LXeiyiisxPlEkUzU6svyS1frIO3Mgxj1fdy7Pm8Ygzguax2N3Fa/D/ag1WqbOprdI+uY6wMUl8/a2G+iag==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  eslint-visitor-keys@4.2.1:
    resolution: {integrity: sha512-Uhdk5sfqcee/9H/rCOJikYz67o0a2Tw2hGRPOG2Y1R2dg7brRe1uG0yaNQDHu+TO/uQPF/5eCapvYSmHUjt7JQ==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  eslint@9.34.0:
    resolution: {integrity: sha512-RNCHRX5EwdrESy3Jc9o8ie8Bog+PeYvvSR8sDGoZxNFTvZ4dlxUB3WzQ3bQMztFrSRODGrLLj8g6OFuGY/aiQg==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    hasBin: true
    peerDependencies:
      jiti: '*'
    peerDependenciesMeta:
      jiti:
        optional: true

  espree@10.4.0:
    resolution: {integrity: sha512-j6PAQ2uUr79PZhBjP5C5fhl8e39FmRnOjsD5lGnWrFU8i2G776tBK7+nP8KuQUTTyAZUwfQqXAgrVH5MbH9CYQ==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  esquery@1.6.0:
    resolution: {integrity: sha512-ca9pw9fomFcKPvFLXhBKUK90ZvGibiGOvRJNbjljY7s7uq/5YO4BOzcYtJqExdx99rF6aAcnRxHmcUHcz6sQsg==}
    engines: {node: '>=0.10'}

  esrecurse@4.3.0:
    resolution: {integrity: sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==}
    engines: {node: '>=4.0'}

  estraverse@5.3.0:
    resolution: {integrity: sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==}
    engines: {node: '>=4.0'}

  esutils@2.0.3:
    resolution: {integrity: sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==}
    engines: {node: '>=0.10.0'}

  eventemitter3@4.0.7:
    resolution: {integrity: sha512-8guHBZCwKnFhYdHr2ysuRWErTwhoN2X8XELRlrRwpmfeY2jjuUN4taQMsULKUVo1K4DvZl+0pgfyoysHxvmvEw==}

  exsolve@1.0.7:
    resolution: {integrity: sha512-VO5fQUzZtI6C+vx4w/4BWJpg3s/5l+6pRQEHzFRM8WFi4XffSP1Z+4qi7GbjWbvRQEbdIco5mIMq+zX4rPuLrw==}

  fast-deep-equal@3.1.3:
    resolution: {integrity: sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==}

  fast-glob@3.3.3:
    resolution: {integrity: sha512-7MptL8U0cqcFdzIzwOTHoilX9x5BrNqye7Z/LuC7kCMRio1EMSyqRK3BEAUD7sXRq4iT4AzTVuZdhgQ2TCvYLg==}
    engines: {node: '>=8.6.0'}

  fast-json-stable-stringify@2.1.0:
    resolution: {integrity: sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==}

  fast-levenshtein@2.0.6:
    resolution: {integrity: sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw==}

  fastq@1.19.1:
    resolution: {integrity: sha512-GwLTyxkCXjXbxqIhTsMI2Nui8huMPtnxg7krajPJAjnEG/iiOS7i+zCtWGZR9G0NBKbXKh6X9m9UIsYX/N6vvQ==}

  fdir@6.5.0:
    resolution: {integrity: sha512-tIbYtZbucOs0BRGqPJkshJUYdL+SDH7dVM8gjy+ERp3WAUjLEFJE+02kanyHtwjWOnwrKYBiwAmM0p4kLJAnXg==}
    engines: {node: '>=12.0.0'}
    peerDependencies:
      picomatch: ^3 || ^4
    peerDependenciesMeta:
      picomatch:
        optional: true

  file-entry-cache@8.0.0:
    resolution: {integrity: sha512-XXTUwCvisa5oacNGRP9SfNtYBNAMi+RPwBFmblZEF7N7swHYQS6/Zfk7SRwx4D5j3CH211YNRco1DEMNVfZCnQ==}
    engines: {node: '>=16.0.0'}

  fill-range@7.1.1:
    resolution: {integrity: sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==}
    engines: {node: '>=8'}

  find-up@5.0.0:
    resolution: {integrity: sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng==}
    engines: {node: '>=10'}

  flat-cache@4.0.1:
    resolution: {integrity: sha512-f7ccFPK3SXFHpx15UIGyRJ/FJQctuKZ0zVuN3frBo4HnK3cay9VEW0R6yPYFHC0AgqhukPzKjq22t5DmAyqGyw==}
    engines: {node: '>=16'}

  flatted@3.3.3:
    resolution: {integrity: sha512-GX+ysw4PBCz0PzosHDepZGANEuFCMLrnRTiEy9McGjmkCQYwRq4A/X786G/fjM/+OjsWSU1ZrY5qyARZmO/uwg==}

  follow-redirects@1.15.11:
    resolution: {integrity: sha512-deG2P0JfjrTxl50XGCDyfI97ZGVCxIpfKYmfyrQ54n5FO/0gfIES8C/Psl6kWVDolizcaaxZJnTS0QSMxvnsBQ==}
    engines: {node: '>=4.0'}
    peerDependencies:
      debug: '*'
    peerDependenciesMeta:
      debug:
        optional: true

  fsevents@2.3.3:
    resolution: {integrity: sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]

  function-bind@1.1.2:
    resolution: {integrity: sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==}

  gensync@1.0.0-beta.2:
    resolution: {integrity: sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==}
    engines: {node: '>=6.9.0'}

  get-intrinsic@1.3.0:
    resolution: {integrity: sha512-9fSjSaos/fRIVIp+xSJlE6lfwhES7LNtKaCBIamHsjr2na1BiABJPo0mOjjz8GJDURarmCPGqaiVg5mfjb98CQ==}
    engines: {node: '>= 0.4'}

  get-proto@1.0.1:
    resolution: {integrity: sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==}
    engines: {node: '>= 0.4'}

  get-tsconfig@4.10.1:
    resolution: {integrity: sha512-auHyJ4AgMz7vgS8Hp3N6HXSmlMdUyhSUrfBF16w153rxtLIEOE+HGqaBppczZvnHLqQJfiHotCYpNhl0lUROFQ==}

  giget@2.0.0:
    resolution: {integrity: sha512-L5bGsVkxJbJgdnwyuheIunkGatUF/zssUoxxjACCseZYAVbaqdh9Tsmmlkl8vYan09H7sbvKt4pS8GqKLBrEzA==}
    hasBin: true

  git-cz@4.9.0:
    resolution: {integrity: sha512-cSRL8IIOXU7UFLdbziCYqg8f8InwLwqHezkiRHNSph7oZqGv0togId1kMTfKil6gzK0VaSXeVBb4oDl0fQCHiw==}
    hasBin: true

  glob-parent@5.1.2:
    resolution: {integrity: sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==}
    engines: {node: '>= 6'}

  glob-parent@6.0.2:
    resolution: {integrity: sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==}
    engines: {node: '>=10.13.0'}

  globals@14.0.0:
    resolution: {integrity: sha512-oahGvuMGQlPw/ivIYBjVSrWAfWLBeku5tpPE2fOPLi+WHffIWbuh2tCjhyQhTBPMf5E9jDEH4FOmTYgYwbKwtQ==}
    engines: {node: '>=18'}

  globals@16.3.0:
    resolution: {integrity: sha512-bqWEnJ1Nt3neqx2q5SFfGS8r/ahumIakg3HcwtNlrVlwXIeNumWn/c7Pn/wKzGhf6SaW6H6uWXLqC30STCMchQ==}
    engines: {node: '>=18'}

  gopd@1.2.0:
    resolution: {integrity: sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg==}
    engines: {node: '>= 0.4'}

  graphemer@1.4.0:
    resolution: {integrity: sha512-EtKwoO6kxCL9WO5xipiHTZlSzBm7WLT627TqC/uVRd0HKmq8NXyebnNYxDoBi7wt8eTWrUrKXCOVaFq9x1kgag==}

  has-flag@4.0.0:
    resolution: {integrity: sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==}
    engines: {node: '>=8'}

  has-passive-events@1.0.0:
    resolution: {integrity: sha512-2vSj6IeIsgvsRMyeQ0JaCX5Q3lX4zMn5HpoVc7MEhQ6pv8Iq9rsXjsp+E5ZwaT7T0xhMT0KmU8gtt1EFVdbJiw==}

  has-symbols@1.1.0:
    resolution: {integrity: sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ==}
    engines: {node: '>= 0.4'}

  hasown@2.0.2:
    resolution: {integrity: sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==}
    engines: {node: '>= 0.4'}

  he@1.2.0:
    resolution: {integrity: sha512-F/1DnUGPopORZi0ni+CvrCgHQ5FyEAHRLSApuYWMmrbSwoN2Mn/7k+Gl38gJnR7yyDZk6WLXwiGod1JOWNDKGw==}
    hasBin: true

  html-encoding-sniffer@3.0.0:
    resolution: {integrity: sha512-oWv4T4yJ52iKrufjnyZPkrN0CH3QnrUqdB6In1g5Fe1mia8GmF36gnfNySxoZtxD5+NmYw1EElVXiBk93UeskA==}
    engines: {node: '>=12'}

  http-proxy@1.18.1:
    resolution: {integrity: sha512-7mz/721AbnJwIVbnaSv1Cz3Am0ZLT/UBwkC92VlxhXv/k/BBQfM2fXElQNC27BVGr0uwUpplYPQM9LnaBMR5NQ==}
    engines: {node: '>=8.0.0'}

  http-server@14.1.1:
    resolution: {integrity: sha512-+cbxadF40UXd9T01zUHgA+rlo2Bg1Srer4+B4NwIHdaGxAGGv59nYRnGGDJ9LBk7alpS0US+J+bLLdQOOkJq4A==}
    engines: {node: '>=12'}
    hasBin: true

  huse@2.0.4:
    resolution: {integrity: sha512-7YBPf1fLfRT/i+TR9JVGxiHCTpL9gQqave8QricjbPZKvzDORVb2zjY/kyoieJoAInd6XJTGkUAI4O10uBgfeQ==}
    peerDependencies:
      react: '>=16.8.0'

  husky@8.0.3:
    resolution: {integrity: sha512-+dQSyqPh4x1hlO1swXBiNb2HzTDN1I2IGLQx1GrBuiqFJfoMrnZWwVmatvSiO+Iz8fBUnf+lekwNo4c2LlXItg==}
    engines: {node: '>=14'}
    hasBin: true

  iconv-lite@0.6.3:
    resolution: {integrity: sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==}
    engines: {node: '>=0.10.0'}

  ignore@5.3.2:
    resolution: {integrity: sha512-hsBTNUqQTDwkWtcdYI2i06Y/nUBEsNEDJKjWdigLvegy8kDuJAS8uRlpkkcQpyEXL0Z/pjDy5HBmMjRCJ2gq+g==}
    engines: {node: '>= 4'}

  ignore@7.0.5:
    resolution: {integrity: sha512-Hs59xBNfUIunMFgWAbGX5cq6893IbWg4KnrjbYwX3tx0ztorVgTDA6B2sxf8ejHJ4wz8BqGUMYlnzNBer5NvGg==}
    engines: {node: '>= 4'}

  immer@9.0.21:
    resolution: {integrity: sha512-bc4NBHqOqSfRW7POMkHd51LvClaeMXpm8dx0e8oE2GORbq5aRK7Bxl4FyzVLdGtLmvLKL7BTDBG5ACQm4HWjTA==}

  import-fresh@3.3.1:
    resolution: {integrity: sha512-TR3KfrTZTYLPB6jUjfx6MF9WcWrHL9su5TObK4ZkYgBdWKPOFoSoQIdEuTuR82pmtxH2spWG9h6etwfr1pLBqQ==}
    engines: {node: '>=6'}

  imurmurhash@0.1.4:
    resolution: {integrity: sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==}
    engines: {node: '>=0.8.19'}

  is-browser@2.1.0:
    resolution: {integrity: sha512-F5rTJxDQ2sW81fcfOR1GnCXT6sVJC104fCyfj+mjpwNEwaPYSn5fte5jiHmBg3DHsIoL/l8Kvw5VN5SsTRcRFQ==}

  is-docker@3.0.0:
    resolution: {integrity: sha512-eljcgEDlEns/7AXFosB5K/2nCM4P7FQPkGc/DWLy5rmFEWvZayGrik1d9/QIY5nJ4f9YsVvBkA6kJpHn9rISdQ==}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}
    hasBin: true

  is-extglob@2.1.1:
    resolution: {integrity: sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==}
    engines: {node: '>=0.10.0'}

  is-glob@4.0.3:
    resolution: {integrity: sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==}
    engines: {node: '>=0.10.0'}

  is-inside-container@1.0.0:
    resolution: {integrity: sha512-KIYLCCJghfHZxqjYBE7rEy0OBuTd5xCHS7tHVgvCLkx7StIoaxwNW3hCALgEUjFfeRk+MG/Qxmp/vtETEF3tRA==}
    engines: {node: '>=14.16'}
    hasBin: true

  is-number@7.0.0:
    resolution: {integrity: sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==}
    engines: {node: '>=0.12.0'}

  is-wsl@3.1.0:
    resolution: {integrity: sha512-UcVfVfaK4Sc4m7X3dUSoHoozQGBEFeDC+zVo06t98xe8CzHSZZBekNXH+tu0NalHolcJ/QAGqS46Hef7QXBIMw==}
    engines: {node: '>=16'}

  isexe@2.0.0:
    resolution: {integrity: sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==}

  jiti@2.5.1:
    resolution: {integrity: sha512-twQoecYPiVA5K/h6SxtORw/Bs3ar+mLUtoPSc7iMXzQzK8d7eJ/R09wmTwAjiamETn1cXYPGfNnu7DMoHgu12w==}
    hasBin: true

  js-tokens@4.0.0:
    resolution: {integrity: sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==}

  js-yaml@4.1.0:
    resolution: {integrity: sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==}
    hasBin: true

  jsesc@3.1.0:
    resolution: {integrity: sha512-/sM3dO2FOzXjKQhJuo0Q173wf2KOo8t4I8vHy6lF9poUp7bKT0/NHE8fPX23PwfhnykfqnC2xRxOnVw5XuGIaA==}
    engines: {node: '>=6'}
    hasBin: true

  json-buffer@3.0.1:
    resolution: {integrity: sha512-4bV5BfR2mqfQTJm+V5tPPdf+ZpuhiIvTuAB5g8kcrXOZpTT/QwwVRWBywX1ozr6lEuPdbHxwaJlm9G6mI2sfSQ==}

  json-schema-traverse@0.4.1:
    resolution: {integrity: sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==}

  json-stable-stringify-without-jsonify@1.0.1:
    resolution: {integrity: sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw==}

  json5@2.2.3:
    resolution: {integrity: sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==}
    engines: {node: '>=6'}
    hasBin: true

  keyv@4.5.4:
    resolution: {integrity: sha512-oxVHkHR/EJf2CNXnWxRLW6mg7JyCCUcG0DtEGmL2ctUo1PNTin1PUil+r/+4r5MpVgC/fn1kjsx7mjSujKqIpw==}

  levn@0.4.1:
    resolution: {integrity: sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ==}
    engines: {node: '>= 0.8.0'}

  locate-path@6.0.0:
    resolution: {integrity: sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw==}
    engines: {node: '>=10'}

  lodash.merge@4.6.2:
    resolution: {integrity: sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==}

  lodash@4.17.21:
    resolution: {integrity: sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==}

  lru-cache@5.1.1:
    resolution: {integrity: sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==}

  magic-string@0.30.19:
    resolution: {integrity: sha512-2N21sPY9Ws53PZvsEpVtNuSW+ScYbQdp4b9qUaL+9QkHUrGFKo56Lg9Emg5s9V/qrtNBmiR01sYhUOwu3H+VOw==}

  math-intrinsics@1.1.0:
    resolution: {integrity: sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g==}
    engines: {node: '>= 0.4'}

  merge2@1.4.1:
    resolution: {integrity: sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==}
    engines: {node: '>= 8'}

  micromatch@4.0.8:
    resolution: {integrity: sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==}
    engines: {node: '>=8.6'}

  mime@1.6.0:
    resolution: {integrity: sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg==}
    engines: {node: '>=4'}
    hasBin: true

  minimatch@3.1.2:
    resolution: {integrity: sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==}

  minimatch@9.0.5:
    resolution: {integrity: sha512-G6T0ZX48xgozx7587koeX9Ys2NYy6Gmv//P89sEte9V9whIapMNF4idKxnW2QtCcLiTWlb/wfCabAtAFWhhBow==}
    engines: {node: '>=16 || 14 >=14.17'}

  minimist@1.2.8:
    resolution: {integrity: sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==}

  mri@1.2.0:
    resolution: {integrity: sha512-tzzskb3bG8LvYGFF/mDTpq3jpI6Q9wc3LEmBaghu+DdCssd1FakN7Bc0hVNmEyGq1bq3RgfkCb3cmQLpNPOroA==}
    engines: {node: '>=4'}

  ms@2.1.3:
    resolution: {integrity: sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==}

  nanoid@3.3.11:
    resolution: {integrity: sha512-N8SpfPUnUp1bK+PMYW8qSWdl9U+wwNWI4QKxOYDy9JAro3WMX7p2OeVRF9v+347pnakNevPmiHhNmZ2HbFA76w==}
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true

  natural-compare@1.4.0:
    resolution: {integrity: sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw==}

  node-fetch-native@1.6.7:
    resolution: {integrity: sha512-g9yhqoedzIUm0nTnTqAQvueMPVOuIY16bqgAJJC8XOOubYFNwz6IER9qs0Gq2Xd0+CecCKFjtdDTMA4u4xG06Q==}

  node-releases@2.0.19:
    resolution: {integrity: sha512-xxOWJsBKtzAq7DY0J+DTzuz58K8e7sJbdgwkbMWQe8UYB6ekmsQ45q0M/tJDsGaZmbC+l7n57UV8Hl5tHxO9uw==}

  nypm@0.6.1:
    resolution: {integrity: sha512-hlacBiRiv1k9hZFiphPUkfSQ/ZfQzZDzC+8z0wL3lvDAOUu/2NnChkKuMoMjNur/9OpKuz2QsIeiPVN0xM5Q0w==}
    engines: {node: ^14.16.0 || >=16.10.0}
    hasBin: true

  object-inspect@1.13.4:
    resolution: {integrity: sha512-W67iLl4J2EXEGTbfeHCffrjDfitvLANg0UlX3wFUUSTx92KXRFegMHUVgSqE+wvhAbi4WqjGg9czysTV2Epbew==}
    engines: {node: '>= 0.4'}

  ofetch@1.4.1:
    resolution: {integrity: sha512-QZj2DfGplQAr2oj9KzceK9Hwz6Whxazmn85yYeVuS3u9XTMOGMRx0kO95MQ+vLsj/S/NwBDMMLU5hpxvI6Tklw==}

  ohash@2.0.11:
    resolution: {integrity: sha512-RdR9FQrFwNBNXAr4GixM8YaRZRJ5PUWbKYbE5eOsrwAjJW0q2REGcf79oYPsLyskQCZG1PLN+S/K1V00joZAoQ==}

  open@10.2.0:
    resolution: {integrity: sha512-YgBpdJHPyQ2UE5x+hlSXcnejzAvD0b22U2OuAP+8OnlJT+PjWPxtgmGqKKc+RgTM63U9gN0YzrYc71R2WT/hTA==}
    engines: {node: '>=18'}

  opener@1.5.2:
    resolution: {integrity: sha512-ur5UIdyw5Y7yEj9wLzhqXiy6GZ3Mwx0yGI+5sMn2r0N0v3cKJvUmFH5yPP+WXh9e0xfyzyJX95D8l088DNFj7A==}
    hasBin: true

  optionator@0.9.4:
    resolution: {integrity: sha512-6IpQ7mKUxRcZNLIObR0hz7lxsapSSIYNZJwXPGeF0mTVqGKFIXj1DQcMoT22S3ROcLyY/rz0PWaWZ9ayWmad9g==}
    engines: {node: '>= 0.8.0'}

  oxc-minify@0.89.0:
    resolution: {integrity: sha512-5+QEY7H8Gx93vlRY/iypZ8ejGs0MDV435DQQMH6ZeKVBiphE1bK1yf11ulgoTz1jQwKXnVpJ1vtRNP+D5slp5w==}
    engines: {node: '>=14.0.0'}

  oxc-parser@0.89.0:
    resolution: {integrity: sha512-BO/RxfooJxuoofjC1USPmZ9a32pzjwXFAM1MHO2zPSij9fkgngHMzAu4t8XY1zrXMmoLBv13fh6LUZ906Rjx+g==}
    engines: {node: '>=20.0.0'}

  oxc-transform@0.89.0:
    resolution: {integrity: sha512-65rusuqi/g5tBB0gcZjM/U9/tSGvaiapus55RXE1AmnTPPEAljSF0z0sm4xDPwCskDPRcvSiJ/cdracCJQ17Sg==}
    engines: {node: '>=14.0.0'}

  p-limit@3.1.0:
    resolution: {integrity: sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==}
    engines: {node: '>=10'}

  p-locate@5.0.0:
    resolution: {integrity: sha512-LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw==}
    engines: {node: '>=10'}

  parent-module@1.0.1:
    resolution: {integrity: sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==}
    engines: {node: '>=6'}

  path-exists@4.0.0:
    resolution: {integrity: sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==}
    engines: {node: '>=8'}

  path-key@3.1.1:
    resolution: {integrity: sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==}
    engines: {node: '>=8'}

  pathe@2.0.3:
    resolution: {integrity: sha512-WUjGcAqP1gQacoQe+OBJsFA7Ld4DyXuUIjZ5cc75cLHvJ7dtNsTugphxIADwspS+AraAUePCKrSVtPLFj/F88w==}

  perfect-debounce@1.0.0:
    resolution: {integrity: sha512-xCy9V055GLEqoFaHoC1SoLIaLmWctgCUaBaWxDZ7/Zx4CTyX7cJQLJOok/orfjZAh9kEYpjJa4d0KcJmCbctZA==}

  picocolors@1.1.1:
    resolution: {integrity: sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==}

  picomatch@2.3.1:
    resolution: {integrity: sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==}
    engines: {node: '>=8.6'}

  picomatch@4.0.3:
    resolution: {integrity: sha512-5gTmgEY/sqK6gFXLIsQNH19lWb4ebPDLA4SdLP7dsWkIXHWlG66oPuVvXSGFPppYZz8ZDZq0dYYrbHfBCVUb1Q==}
    engines: {node: '>=12'}

  pkg-types@2.3.0:
    resolution: {integrity: sha512-SIqCzDRg0s9npO5XQ3tNZioRY1uK06lA41ynBC1YmFTmnY6FjUjVt6s4LoADmwoig1qqD0oK8h1p/8mlMx8Oig==}

  portfinder@1.0.37:
    resolution: {integrity: sha512-yuGIEjDAYnnOex9ddMnKZEMFE0CcGo6zbfzDklkmT1m5z734ss6JMzN9rNB3+RR7iS+F10D4/BVIaXOyh8PQKw==}
    engines: {node: '>= 10.12'}

  postcss@8.5.6:
    resolution: {integrity: sha512-3Ybi1tAuwAP9s0r1UQ2J4n5Y0G05bJkpUIO0/bI9MhwmD70S5aTWbXGBwxHrelT+XM1k6dM0pk+SwNkpTRN7Pg==}
    engines: {node: ^10 || ^12 || >=14}

  prelude-ls@1.2.1:
    resolution: {integrity: sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g==}
    engines: {node: '>= 0.8.0'}

  pretty-bytes@7.0.1:
    resolution: {integrity: sha512-285/jRCYIbMGDciDdrw0KPNC4LKEEwz/bwErcYNxSJOi4CpGUuLpb9gQpg3XJP0XYj9ldSRluXxih4lX2YN8Xw==}
    engines: {node: '>=20'}

  punycode@2.3.1:
    resolution: {integrity: sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==}
    engines: {node: '>=6'}

  qs@6.14.0:
    resolution: {integrity: sha512-YWWTjgABSKcvs/nWBi9PycY/JiPJqOD4JA6o9Sej2AtvSGarXxKC3OQSk4pAarbdQlKAh5D4FCQkJNkW+GAn3w==}
    engines: {node: '>=0.6'}

  query-shape@1.0.2:
    resolution: {integrity: sha512-6p5K0EABPhrc4GpoE7D6zQgiJs59hk3GzFoHOUGXuZi8tOaX2KlMB7EFmH9hxowRVg8Rxmu4BkMuGxljjTqrIQ==}

  queue-microtask@1.2.3:
    resolution: {integrity: sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==}

  rc9@2.1.2:
    resolution: {integrity: sha512-btXCnMmRIBINM2LDZoEmOogIZU7Qe7zn4BpomSKZ/ykbLObuBdvG+mFq11DL6fjH1DRwHhrlgtYWG96bJiC7Cg==}

  react-dom@19.1.1:
    resolution: {integrity: sha512-Dlq/5LAZgF0Gaz6yiqZCf6VCcZs1ghAJyrsu84Q/GT0gV+mCxbfmKNoGRKBYMJ8IEdGPqu49YWXD02GCknEDkw==}
    peerDependencies:
      react: ^19.1.1

  react-refresh@0.17.0:
    resolution: {integrity: sha512-z6F7K9bV85EfseRCp2bzrpyQ0Gkw1uLoCel9XBVWPg/TjRj94SkJzUTGfOa4bs7iJvBWtQG0Wq7wnI0syw3EBQ==}
    engines: {node: '>=0.10.0'}

  react@19.1.1:
    resolution: {integrity: sha512-w8nqGImo45dmMIfljjMwOGtbmC/mk4CMYhWIicdSflH91J9TyCyczcPFXJzrZ/ZXcgGRFeP6BU0BEJTw6tZdfQ==}
    engines: {node: '>=0.10.0'}

  readdirp@4.1.2:
    resolution: {integrity: sha512-GDhwkLfywWL2s6vEjyhri+eXmfH6j1L7JE27WhqLeYzoh/A3DBaYGEj2H/HFZCn/kMfim73FXxEJTw06WtxQwg==}
    engines: {node: '>= 14.18.0'}

  requires-port@1.0.0:
    resolution: {integrity: sha512-KigOCHcocU3XODJxsu8i/j8T9tzT4adHiecwORRQ0ZZFcp7ahwXuRU1m+yuO90C5ZUyGeGfocHDI14M3L3yDAQ==}

  resize-detector@0.3.0:
    resolution: {integrity: sha512-R/tCuvuOHQ8o2boRP6vgx8hXCCy87H1eY9V5imBYeVNyNVpuL9ciReSccLj2gDcax9+2weXy3bc8Vv+NRXeEvQ==}

  resolve-from@4.0.0:
    resolution: {integrity: sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==}
    engines: {node: '>=4'}

  resolve-pkg-maps@1.0.0:
    resolution: {integrity: sha512-seS2Tj26TBVOC2NIc2rOe2y2ZO7efxITtLZcGSOnHHNOQ7CkiUBfw0Iw2ck6xkIhPwLhKNLS8BO+hEpngQlqzw==}

  reusify@1.1.0:
    resolution: {integrity: sha512-g6QUff04oZpHs0eG5p83rFLhHeV00ug/Yf9nZM6fLeUrPguBTkTQOdpAWWspMh55TZfVQDPaN3NQJfbVRAxdIw==}
    engines: {iojs: '>=1.0.0', node: '>=0.10.0'}

  robuild@0.0.5:
    resolution: {integrity: sha512-q0AvZIIwTFwygAvtKj09JnyFT+Our1Dy3s9vfazmn+02MX9CAk/QkFDhdQnHJCWhV0ReruXnBa6D0IjDXg92KQ==}
    hasBin: true

  rolldown-plugin-dts@0.16.5:
    resolution: {integrity: sha512-bOAfJ7Tc11xK/Uou7KWYha25/Sy80G0DZkhX8WMYx6l8PUalR+bvVzQNuEqXafpKEisZfUHQrkhS2gZG76Xntw==}
    engines: {node: '>=20.18.0'}
    peerDependencies:
      '@ts-macro/tsc': ^0.3.6
      '@typescript/native-preview': '>=7.0.0-dev.20250601.1'
      rolldown: ^1.0.0-beta.9
      typescript: ^5.0.0
      vue-tsc: ~3.0.3
    peerDependenciesMeta:
      '@ts-macro/tsc':
        optional: true
      '@typescript/native-preview':
        optional: true
      typescript:
        optional: true
      vue-tsc:
        optional: true

  rolldown@1.0.0-beta.30:
    resolution: {integrity: sha512-H/LmDTUPlm65hWOTjXvd1k0qrGinNi8LrG3JsHVm6Oit7STg0upBmgoG5PZUHbAnGTHr0MLoLyzjmH261lIqSg==}
    hasBin: true

  rollup@4.49.0:
    resolution: {integrity: sha512-3IVq0cGJ6H7fKXXEdVt+RcYvRCt8beYY9K1760wGQwSAHZcS9eot1zDG5axUbcp/kWRi5zKIIDX8MoKv/TzvZA==}
    engines: {node: '>=18.0.0', npm: '>=8.0.0'}
    hasBin: true

  run-applescript@7.0.0:
    resolution: {integrity: sha512-9by4Ij99JUr/MCFBUkDKLWK3G9HVXmabKz9U5MlIAIuvuzkiOicRYs8XJLxX+xahD+mLiiCYDqF9dKAgtzKP1A==}
    engines: {node: '>=18'}

  run-parallel@1.2.0:
    resolution: {integrity: sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==}

  safe-buffer@5.1.2:
    resolution: {integrity: sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g==}

  safer-buffer@2.1.2:
    resolution: {integrity: sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==}

  scheduler@0.26.0:
    resolution: {integrity: sha512-NlHwttCI/l5gCPR3D1nNXtWABUmBwvZpEQiD4IXSbIDq8BzLIK/7Ir5gTFSGZDUu37K5cMNp0hFtzO38sC7gWA==}

  scule@1.3.0:
    resolution: {integrity: sha512-6FtHJEvt+pVMIB9IBY+IcCJ6Z5f1iQnytgyfKMhDKgmzYG+TeH/wx1y3l27rshSbLiSanrR9ffZDrEsmjlQF2g==}

  secure-compare@3.0.1:
    resolution: {integrity: sha512-AckIIV90rPDcBcglUwXPF3kg0P0qmPsPXAj6BBEENQE1p5yA1xfmDJzfi1Tappj37Pv2mVbKpL3Z1T+Nn7k1Qw==}

  semver@6.3.1:
    resolution: {integrity: sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==}
    hasBin: true

  semver@7.7.2:
    resolution: {integrity: sha512-RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA==}
    engines: {node: '>=10'}
    hasBin: true

  shallowequal@1.1.0:
    resolution: {integrity: sha512-y0m1JoUZSlPAjXVtPPW70aZWfIL/dSP7AFkRnniLCrK/8MDKog3TySTBmckD+RObVxH0v4Tox67+F14PdED2oQ==}

  shebang-command@2.0.0:
    resolution: {integrity: sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==}
    engines: {node: '>=8'}

  shebang-regex@3.0.0:
    resolution: {integrity: sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==}
    engines: {node: '>=8'}

  side-channel-list@1.0.0:
    resolution: {integrity: sha512-FCLHtRD/gnpCiCHEiJLOwdmFP+wzCmDEkc9y7NsYxeF4u7Btsn1ZuwgwJGxImImHicJArLP4R0yX4c2KCrMrTA==}
    engines: {node: '>= 0.4'}

  side-channel-map@1.0.1:
    resolution: {integrity: sha512-VCjCNfgMsby3tTdo02nbjtM/ewra6jPHmpThenkTYh8pG9ucZ/1P8So4u4FGBek/BjpOVsDCMoLA/iuBKIFXRA==}
    engines: {node: '>= 0.4'}

  side-channel-weakmap@1.0.2:
    resolution: {integrity: sha512-WPS/HvHQTYnHisLo9McqBHOJk2FkHO/tlpvldyrnem4aeQp4hai3gythswg6p01oSoTl58rcpiFAjF2br2Ak2A==}
    engines: {node: '>= 0.4'}

  side-channel@1.1.0:
    resolution: {integrity: sha512-ZX99e6tRweoUXqR+VBrslhda51Nh5MTQwou5tnUDgbtyM0dBgmhEDtWGP/xbKn6hqfPRHujUNwz5fy/wbbhnpw==}
    engines: {node: '>= 0.4'}

  source-map-js@1.2.1:
    resolution: {integrity: sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==}
    engines: {node: '>=0.10.0'}

  std-env@3.9.0:
    resolution: {integrity: sha512-UGvjygr6F6tpH7o2qyqR6QYpwraIjKSdtzyBdyytFOHmPZY917kwdwLG0RbOjWOnKmnm3PeHjaoLLMie7kPLQw==}

  strip-json-comments@3.1.1:
    resolution: {integrity: sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==}
    engines: {node: '>=8'}

  supports-color@7.2.0:
    resolution: {integrity: sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==}
    engines: {node: '>=8'}

  tinyexec@1.0.1:
    resolution: {integrity: sha512-5uC6DDlmeqiOwCPmK9jMSdOuZTh8bU39Ys6yidB+UTt5hfZUPGAypSgFRiEp+jbi9qH40BLDvy85jIU88wKSqw==}

  tinyglobby@0.2.14:
    resolution: {integrity: sha512-tX5e7OM1HnYr2+a2C/4V0htOcSQcoSTH9KgJnVvNm5zm/cyEWKJ7j7YutsH9CxMdtOkkLFy2AHrMci9IM8IPZQ==}
    engines: {node: '>=12.0.0'}

  to-regex-range@5.0.1:
    resolution: {integrity: sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==}
    engines: {node: '>=8.0'}

  ts-api-utils@2.1.0:
    resolution: {integrity: sha512-CUgTZL1irw8u29bzrOD/nH85jqyc74D6SshFgujOIA7osm2Rz7dYH77agkx7H4FBNxDq7Cjf+IjaX/8zwFW+ZQ==}
    engines: {node: '>=18.12'}
    peerDependencies:
      typescript: '>=4.8.4'

  tslib@2.8.1:
    resolution: {integrity: sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==}

  turbo-darwin-64@2.5.6:
    resolution: {integrity: sha512-3C1xEdo4aFwMJAPvtlPqz1Sw/+cddWIOmsalHFMrsqqydcptwBfu26WW2cDm3u93bUzMbBJ8k3zNKFqxJ9ei2A==}
    cpu: [x64]
    os: [darwin]

  turbo-darwin-arm64@2.5.6:
    resolution: {integrity: sha512-LyiG+rD7JhMfYwLqB6k3LZQtYn8CQQUePbpA8mF/hMLPAekXdJo1g0bUPw8RZLwQXUIU/3BU7tXENvhSGz5DPA==}
    cpu: [arm64]
    os: [darwin]

  turbo-linux-64@2.5.6:
    resolution: {integrity: sha512-GOcUTT0xiT/pSnHL4YD6Yr3HreUhU8pUcGqcI2ksIF9b2/r/kRHwGFcsHgpG3+vtZF/kwsP0MV8FTlTObxsYIA==}
    cpu: [x64]
    os: [linux]

  turbo-linux-arm64@2.5.6:
    resolution: {integrity: sha512-10Tm15bruJEA3m0V7iZcnQBpObGBcOgUcO+sY7/2vk1bweW34LMhkWi8svjV9iDF68+KJDThnYDlYE/bc7/zzQ==}
    cpu: [arm64]
    os: [linux]

  turbo-windows-64@2.5.6:
    resolution: {integrity: sha512-FyRsVpgaj76It0ludwZsNN40ytHN+17E4PFJyeliBEbxrGTc5BexlXVpufB7XlAaoaZVxbS6KT8RofLfDRyEPg==}
    cpu: [x64]
    os: [win32]

  turbo-windows-arm64@2.5.6:
    resolution: {integrity: sha512-j/tWu8cMeQ7HPpKri6jvKtyXg9K1gRyhdK4tKrrchH8GNHscPX/F71zax58yYtLRWTiK04zNzPcUJuoS0+v/+Q==}
    cpu: [arm64]
    os: [win32]

  turbo@2.5.6:
    resolution: {integrity: sha512-gxToHmi9oTBNB05UjUsrWf0OyN5ZXtD0apOarC1KIx232Vp3WimRNy3810QzeNSgyD5rsaIDXlxlbnOzlouo+w==}
    hasBin: true

  type-check@0.4.0:
    resolution: {integrity: sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew==}
    engines: {node: '>= 0.8.0'}

  typescript-eslint@8.41.0:
    resolution: {integrity: sha512-n66rzs5OBXW3SFSnZHr2T685q1i4ODm2nulFJhMZBotaTavsS8TrI3d7bDlRSs9yWo7HmyWrN9qDu14Qv7Y0Dw==}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <6.0.0'

  typescript@5.8.3:
    resolution: {integrity: sha512-p1diW6TqL9L07nNxvRMM7hMMw4c5XOo/1ibL4aAIGmSAt9slTE1Xgw5KWuof2uTOvCg9BY7ZRi+GaF+7sfgPeQ==}
    engines: {node: '>=14.17'}
    hasBin: true

  ufo@1.6.1:
    resolution: {integrity: sha512-9a4/uxlTWJ4+a5i0ooc1rU7C7YOw3wT+UGqdeNNHWnOF9qcMBgLRS+4IYUqbczewFx4mLEig6gawh7X6mFlEkA==}

  undici-types@5.26.5:
    resolution: {integrity: sha512-JlCMO+ehdEIKqlFxk6IfVoAUVmgz7cU7zD/h9XZ0qzeosSHmUJVOzSQvvYSYWXkFXC+IfLKSIffhv0sVZup6pA==}

  union@0.5.0:
    resolution: {integrity: sha512-N6uOhuW6zO95P3Mel2I2zMsbsanvvtgn6jVqJv4vbVcz/JN0OkL9suomjQGmWtxJQXOCqUJvquc1sMeNz/IwlA==}
    engines: {node: '>= 0.8.0'}

  update-browserslist-db@1.1.3:
    resolution: {integrity: sha512-UxhIZQ+QInVdunkDAaiazvvT/+fXL5Osr0JZlJulepYu6Jd7qJtDZjlur0emRlT71EN3ScPoE7gvsuIKKNavKw==}
    hasBin: true
    peerDependencies:
      browserslist: '>= 4.21.0'

  uri-js@4.4.1:
    resolution: {integrity: sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==}

  url-join@4.0.1:
    resolution: {integrity: sha512-jk1+QP6ZJqyOiuEI9AEWQfju/nB2Pw466kbA0LEZljHwKeMgd9WrAEgEGxjPDD2+TNbbb37rTyhEfrCXfuKXnA==}

  use-immer@0.6.0:
    resolution: {integrity: sha512-dFGRfvWCqPDTOt/S431ETYTg6+uxbpb7A1pptufwXVzGJY3RlXr38+3wyLNpc6SbbmAKjWl6+EP6uW74fkEsXQ==}
    peerDependencies:
      immer: '>=2.0.0'
      react: ^16.8.0 || ^17.0.1

  user-attention@1.0.3:
    resolution: {integrity: sha512-ZSb/dqTRZWfkM9xeP7Zj+f3mH86Trgm6q1isi6qTV2t+tEOPH0JKBit+nJCN9sejwZJlQmedPNniR09/6Q7LPQ==}

  vite@7.1.3:
    resolution: {integrity: sha512-OOUi5zjkDxYrKhTV3V7iKsoS37VUM7v40+HuwEmcrsf11Cdx9y3DIr2Px6liIcZFwt3XSRpQvFpL3WVy7ApkGw==}
    engines: {node: ^20.19.0 || >=22.12.0}
    hasBin: true
    peerDependencies:
      '@types/node': ^20.19.0 || >=22.12.0
      jiti: '>=1.21.0'
      less: ^4.0.0
      lightningcss: ^1.21.0
      sass: ^1.70.0
      sass-embedded: ^1.70.0
      stylus: '>=0.54.8'
      sugarss: ^5.0.0
      terser: ^5.16.0
      tsx: ^4.8.1
      yaml: ^2.4.2
    peerDependenciesMeta:
      '@types/node':
        optional: true
      jiti:
        optional: true
      less:
        optional: true
      lightningcss:
        optional: true
      sass:
        optional: true
      sass-embedded:
        optional: true
      stylus:
        optional: true
      sugarss:
        optional: true
      terser:
        optional: true
      tsx:
        optional: true
      yaml:
        optional: true

  whatwg-encoding@2.0.0:
    resolution: {integrity: sha512-p41ogyeMUrw3jWclHWTQg1k05DSVXPLcVxRTYsXUk+ZooOCZLcoYgPZ/HL/D/N+uQPOtcp1me1WhBEaX02mhWg==}
    engines: {node: '>=12'}

  which@2.0.2:
    resolution: {integrity: sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==}
    engines: {node: '>= 8'}
    hasBin: true

  word-wrap@1.2.5:
    resolution: {integrity: sha512-BN22B5eaMMI9UMtjrGd5g5eCYPpCPDUy0FJXbYsaT5zYxjFOckS53SQDE3pWkVoWpHXVb3BrYcEN4Twa55B5cA==}
    engines: {node: '>=0.10.0'}

  wsl-utils@0.1.0:
    resolution: {integrity: sha512-h3Fbisa2nKGPxCpm89Hk33lBLsnaGBvctQopaBSOW/uIs6FTe1ATyAnKFJrzVs9vpGdsTe73WF3V4lIsk4Gacw==}
    engines: {node: '>=18'}

  yallist@3.1.1:
    resolution: {integrity: sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==}

  yaml@2.8.1:
    resolution: {integrity: sha512-lcYcMxX2PO9XMGvAJkJ3OsNMw+/7FKes7/hgerGUYWIoWu5j/+YQqcZr5JnPZWzOsEBgMbSbiSTn/dv/69Mkpw==}
    engines: {node: '>= 14.6'}
    hasBin: true

  yocto-queue@0.1.0:
    resolution: {integrity: sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==}
    engines: {node: '>=10'}

snapshots:

  '@ampproject/remapping@2.3.0':
    dependencies:
      '@jridgewell/gen-mapping': 0.3.13
      '@jridgewell/trace-mapping': 0.3.30

  '@babel/code-frame@7.27.1':
    dependencies:
      '@babel/helper-validator-identifier': 7.27.1
      js-tokens: 4.0.0
      picocolors: 1.1.1

  '@babel/compat-data@7.28.0': {}

  '@babel/core@7.28.3':
    dependencies:
      '@ampproject/remapping': 2.3.0
      '@babel/code-frame': 7.27.1
      '@babel/generator': 7.28.3
      '@babel/helper-compilation-targets': 7.27.2
      '@babel/helper-module-transforms': 7.28.3(@babel/core@7.28.3)
      '@babel/helpers': 7.28.3
      '@babel/parser': 7.28.3
      '@babel/template': 7.27.2
      '@babel/traverse': 7.28.3
      '@babel/types': 7.28.2
      convert-source-map: 2.0.0
      debug: 4.4.1
      gensync: 1.0.0-beta.2
      json5: 2.2.3
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  '@babel/generator@7.28.3':
    dependencies:
      '@babel/parser': 7.28.3
      '@babel/types': 7.28.2
      '@jridgewell/gen-mapping': 0.3.13
      '@jridgewell/trace-mapping': 0.3.30
      jsesc: 3.1.0

  '@babel/helper-compilation-targets@7.27.2':
    dependencies:
      '@babel/compat-data': 7.28.0
      '@babel/helper-validator-option': 7.27.1
      browserslist: 4.25.3
      lru-cache: 5.1.1
      semver: 6.3.1

  '@babel/helper-globals@7.28.0': {}

  '@babel/helper-module-imports@7.27.1':
    dependencies:
      '@babel/traverse': 7.28.3
      '@babel/types': 7.28.2
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-module-transforms@7.28.3(@babel/core@7.28.3)':
    dependencies:
      '@babel/core': 7.28.3
      '@babel/helper-module-imports': 7.27.1
      '@babel/helper-validator-identifier': 7.27.1
      '@babel/traverse': 7.28.3
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-plugin-utils@7.27.1': {}

  '@babel/helper-string-parser@7.27.1': {}

  '@babel/helper-validator-identifier@7.27.1': {}

  '@babel/helper-validator-option@7.27.1': {}

  '@babel/helpers@7.28.3':
    dependencies:
      '@babel/template': 7.27.2
      '@babel/types': 7.28.2

  '@babel/parser@7.28.3':
    dependencies:
      '@babel/types': 7.28.2

  '@babel/parser@7.28.4':
    dependencies:
      '@babel/types': 7.28.4

  '@babel/plugin-transform-react-jsx-self@7.27.1(@babel/core@7.28.3)':
    dependencies:
      '@babel/core': 7.28.3
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-react-jsx-source@7.27.1(@babel/core@7.28.3)':
    dependencies:
      '@babel/core': 7.28.3
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/template@7.27.2':
    dependencies:
      '@babel/code-frame': 7.27.1
      '@babel/parser': 7.28.3
      '@babel/types': 7.28.2

  '@babel/traverse@7.28.3':
    dependencies:
      '@babel/code-frame': 7.27.1
      '@babel/generator': 7.28.3
      '@babel/helper-globals': 7.28.0
      '@babel/parser': 7.28.3
      '@babel/template': 7.27.2
      '@babel/types': 7.28.2
      debug: 4.4.1
    transitivePeerDependencies:
      - supports-color

  '@babel/types@7.28.2':
    dependencies:
      '@babel/helper-string-parser': 7.27.1
      '@babel/helper-validator-identifier': 7.27.1

  '@babel/types@7.28.4':
    dependencies:
      '@babel/helper-string-parser': 7.27.1
      '@babel/helper-validator-identifier': 7.27.1

  '@emnapi/core@1.5.0':
    dependencies:
      '@emnapi/wasi-threads': 1.1.0
      tslib: 2.8.1
    optional: true

  '@emnapi/runtime@1.5.0':
    dependencies:
      tslib: 2.8.1
    optional: true

  '@emnapi/wasi-threads@1.1.0':
    dependencies:
      tslib: 2.8.1
    optional: true

  '@esbuild/aix-ppc64@0.25.9':
    optional: true

  '@esbuild/android-arm64@0.25.9':
    optional: true

  '@esbuild/android-arm@0.25.9':
    optional: true

  '@esbuild/android-x64@0.25.9':
    optional: true

  '@esbuild/darwin-arm64@0.25.9':
    optional: true

  '@esbuild/darwin-x64@0.25.9':
    optional: true

  '@esbuild/freebsd-arm64@0.25.9':
    optional: true

  '@esbuild/freebsd-x64@0.25.9':
    optional: true

  '@esbuild/linux-arm64@0.25.9':
    optional: true

  '@esbuild/linux-arm@0.25.9':
    optional: true

  '@esbuild/linux-ia32@0.25.9':
    optional: true

  '@esbuild/linux-loong64@0.25.9':
    optional: true

  '@esbuild/linux-mips64el@0.25.9':
    optional: true

  '@esbuild/linux-ppc64@0.25.9':
    optional: true

  '@esbuild/linux-riscv64@0.25.9':
    optional: true

  '@esbuild/linux-s390x@0.25.9':
    optional: true

  '@esbuild/linux-x64@0.25.9':
    optional: true

  '@esbuild/netbsd-arm64@0.25.9':
    optional: true

  '@esbuild/netbsd-x64@0.25.9':
    optional: true

  '@esbuild/openbsd-arm64@0.25.9':
    optional: true

  '@esbuild/openbsd-x64@0.25.9':
    optional: true

  '@esbuild/openharmony-arm64@0.25.9':
    optional: true

  '@esbuild/sunos-x64@0.25.9':
    optional: true

  '@esbuild/win32-arm64@0.25.9':
    optional: true

  '@esbuild/win32-ia32@0.25.9':
    optional: true

  '@esbuild/win32-x64@0.25.9':
    optional: true

  '@eslint-community/eslint-utils@4.7.0(eslint@9.34.0(jiti@2.5.1))':
    dependencies:
      eslint: 9.34.0(jiti@2.5.1)
      eslint-visitor-keys: 3.4.3

  '@eslint-community/regexpp@4.12.1': {}

  '@eslint/config-array@0.21.0':
    dependencies:
      '@eslint/object-schema': 2.1.6
      debug: 4.4.1
      minimatch: 3.1.2
    transitivePeerDependencies:
      - supports-color

  '@eslint/config-helpers@0.3.1': {}

  '@eslint/core@0.15.2':
    dependencies:
      '@types/json-schema': 7.0.15

  '@eslint/eslintrc@3.3.1':
    dependencies:
      ajv: 6.12.6
      debug: 4.4.1
      espree: 10.4.0
      globals: 14.0.0
      ignore: 5.3.2
      import-fresh: 3.3.1
      js-yaml: 4.1.0
      minimatch: 3.1.2
      strip-json-comments: 3.1.1
    transitivePeerDependencies:
      - supports-color

  '@eslint/js@9.34.0': {}

  '@eslint/object-schema@2.1.6': {}

  '@eslint/plugin-kit@0.3.5':
    dependencies:
      '@eslint/core': 0.15.2
      levn: 0.4.1

  '@humanfs/core@0.19.1': {}

  '@humanfs/node@0.16.6':
    dependencies:
      '@humanfs/core': 0.19.1
      '@humanwhocodes/retry': 0.3.1

  '@humanwhocodes/module-importer@1.0.1': {}

  '@humanwhocodes/retry@0.3.1': {}

  '@humanwhocodes/retry@0.4.3': {}

  '@huse/action-pending@1.1.2(react@19.1.1)':
    dependencies:
      '@huse/number': 1.2.2(react@19.1.1)
      react: 19.1.1

  '@huse/boolean@1.2.0(react@19.1.1)':
    dependencies:
      '@huse/methods': 1.2.2(react@19.1.1)
      react: 19.1.1

  '@huse/click-outside@1.1.1(react@19.1.1)':
    dependencies:
      '@huse/document-event': 1.1.1(react@19.1.1)
      react: 19.1.1

  '@huse/collection@1.1.2(react@19.1.1)':
    dependencies:
      '@huse/methods': 1.2.2(react@19.1.1)
      react: 19.1.1

  '@huse/debounce@1.1.2(react@19.1.1)':
    dependencies:
      debounce: 1.2.1
      react: 19.1.1

  '@huse/debug@1.1.1(react@19.1.1)':
    dependencies:
      '@huse/previous-value': 1.1.1(react@19.1.1)
      fast-deep-equal: 3.1.3
      lodash: 4.17.21
      react: 19.1.1
      shallowequal: 1.1.0

  '@huse/derived-state@1.1.1(react@19.1.1)':
    dependencies:
      react: 19.1.1

  '@huse/document-event@1.1.1(react@19.1.1)':
    dependencies:
      react: 19.1.1

  '@huse/document-title@1.1.1(react@19.1.1)':
    dependencies:
      react: 19.1.1

  '@huse/effect-ref@1.1.1(react@19.1.1)':
    dependencies:
      react: 19.1.1

  '@huse/element-size@1.1.1(react@19.1.1)':
    dependencies:
      '@huse/effect-ref': 1.1.1(react@19.1.1)
      react: 19.1.1
      resize-detector: 0.3.0

  '@huse/hover@1.1.3(react@19.1.1)':
    dependencies:
      '@huse/boolean': 1.2.0(react@19.1.1)
      react: 19.1.1

  '@huse/infinite-scroll@1.2.2(react@19.1.1)':
    dependencies:
      '@huse/methods': 1.2.2(react@19.1.1)
      react: 19.1.1

  '@huse/input-value@1.1.1(react@19.1.1)':
    dependencies:
      react: 19.1.1

  '@huse/intersection@1.2.1(react@19.1.1)':
    dependencies:
      '@huse/effect-ref': 1.1.1(react@19.1.1)
      react: 19.1.1

  '@huse/local-storage@1.1.1(react@19.1.1)':
    dependencies:
      react: 19.1.1

  '@huse/media@1.1.1(react@19.1.1)':
    dependencies:
      react: 19.1.1

  '@huse/merged-ref@1.2.1(react@19.1.1)':
    dependencies:
      '@huse/previous-value': 1.1.1(react@19.1.1)
      react: 19.1.1

  '@huse/methods@1.2.2(react@19.1.1)':
    dependencies:
      immer: 9.0.21
      react: 19.1.1
      use-immer: 0.6.0(immer@9.0.21)(react@19.1.1)

  '@huse/network@1.1.3(react@19.1.1)':
    dependencies:
      '@huse/boolean': 1.2.0(react@19.1.1)
      react: 19.1.1

  '@huse/number@1.2.2(react@19.1.1)':
    dependencies:
      '@huse/methods': 1.2.2(react@19.1.1)
      react: 19.1.1

  '@huse/optimistic@0.10.1(react@19.1.1)':
    dependencies:
      react: 19.1.1

  '@huse/performance@1.1.1(react@19.1.1)':
    dependencies:
      react: 19.1.1

  '@huse/poll@1.1.2(react@19.1.1)':
    dependencies:
      '@huse/action-pending': 1.1.2(react@19.1.1)
      '@huse/timeout': 1.1.1(react@19.1.1)
      react: 19.1.1
      user-attention: 1.0.3

  '@huse/previous-value@1.1.1(react@19.1.1)':
    dependencies:
      fast-deep-equal: 3.1.3
      react: 19.1.1
      shallowequal: 1.1.0

  '@huse/request@1.3.0(react@19.1.1)':
    dependencies:
      '@huse/previous-value': 1.1.1(react@19.1.1)
      '@huse/update': 1.1.1(react@19.1.1)
      query-shape: 1.0.2
      react: 19.1.1

  '@huse/script@1.1.1(react@19.1.1)':
    dependencies:
      '@huse/update': 1.1.1(react@19.1.1)
      react: 19.1.1

  '@huse/scroll-into-view@1.1.1(react@19.1.1)':
    dependencies:
      react: 19.1.1

  '@huse/scroll-lock@1.1.1(react@19.1.1)':
    dependencies:
      react: 19.1.1

  '@huse/scroll-position@1.1.1(react@19.1.1)':
    dependencies:
      has-passive-events: 1.0.0
      react: 19.1.1

  '@huse/selection@1.1.2(react@19.1.1)':
    dependencies:
      '@huse/collection': 1.1.2(react@19.1.1)
      react: 19.1.1

  '@huse/snapshot@1.1.2(react@19.1.1)':
    dependencies:
      '@huse/debounce': 1.1.2(react@19.1.1)
      react: 19.1.1

  '@huse/timeout@1.1.1(react@19.1.1)':
    dependencies:
      react: 19.1.1

  '@huse/transition-state@1.1.1(react@19.1.1)':
    dependencies:
      react: 19.1.1

  '@huse/update@1.1.1(react@19.1.1)':
    dependencies:
      react: 19.1.1

  '@huse/user-media@0.10.1(react@19.1.1)':
    dependencies:
      '@huse/previous-value': 1.1.1(react@19.1.1)
      react: 19.1.1

  '@huse/web-socket@0.11.1(react@19.1.1)':
    dependencies:
      '@huse/previous-value': 1.1.1(react@19.1.1)
      '@types/node': 18.19.123
      react: 19.1.1

  '@huse/window-size@1.1.1(react@19.1.1)':
    dependencies:
      react: 19.1.1

  '@jridgewell/gen-mapping@0.3.13':
    dependencies:
      '@jridgewell/sourcemap-codec': 1.5.5
      '@jridgewell/trace-mapping': 0.3.30

  '@jridgewell/resolve-uri@3.1.2': {}

  '@jridgewell/sourcemap-codec@1.5.5': {}

  '@jridgewell/trace-mapping@0.3.30':
    dependencies:
      '@jridgewell/resolve-uri': 3.1.2
      '@jridgewell/sourcemap-codec': 1.5.5

  '@napi-rs/wasm-runtime@1.0.5':
    dependencies:
      '@emnapi/core': 1.5.0
      '@emnapi/runtime': 1.5.0
      '@tybys/wasm-util': 0.10.1
    optional: true

  '@nodelib/fs.scandir@2.1.5':
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      run-parallel: 1.2.0

  '@nodelib/fs.stat@2.0.5': {}

  '@nodelib/fs.walk@1.2.8':
    dependencies:
      '@nodelib/fs.scandir': 2.1.5
      fastq: 1.19.1

  '@oxc-minify/binding-android-arm64@0.89.0':
    optional: true

  '@oxc-minify/binding-darwin-arm64@0.89.0':
    optional: true

  '@oxc-minify/binding-darwin-x64@0.89.0':
    optional: true

  '@oxc-minify/binding-freebsd-x64@0.89.0':
    optional: true

  '@oxc-minify/binding-linux-arm-gnueabihf@0.89.0':
    optional: true

  '@oxc-minify/binding-linux-arm-musleabihf@0.89.0':
    optional: true

  '@oxc-minify/binding-linux-arm64-gnu@0.89.0':
    optional: true

  '@oxc-minify/binding-linux-arm64-musl@0.89.0':
    optional: true

  '@oxc-minify/binding-linux-riscv64-gnu@0.89.0':
    optional: true

  '@oxc-minify/binding-linux-s390x-gnu@0.89.0':
    optional: true

  '@oxc-minify/binding-linux-x64-gnu@0.89.0':
    optional: true

  '@oxc-minify/binding-linux-x64-musl@0.89.0':
    optional: true

  '@oxc-minify/binding-wasm32-wasi@0.89.0':
    dependencies:
      '@napi-rs/wasm-runtime': 1.0.5
    optional: true

  '@oxc-minify/binding-win32-arm64-msvc@0.89.0':
    optional: true

  '@oxc-minify/binding-win32-x64-msvc@0.89.0':
    optional: true

  '@oxc-parser/binding-android-arm64@0.89.0':
    optional: true

  '@oxc-parser/binding-darwin-arm64@0.89.0':
    optional: true

  '@oxc-parser/binding-darwin-x64@0.89.0':
    optional: true

  '@oxc-parser/binding-freebsd-x64@0.89.0':
    optional: true

  '@oxc-parser/binding-linux-arm-gnueabihf@0.89.0':
    optional: true

  '@oxc-parser/binding-linux-arm-musleabihf@0.89.0':
    optional: true

  '@oxc-parser/binding-linux-arm64-gnu@0.89.0':
    optional: true

  '@oxc-parser/binding-linux-arm64-musl@0.89.0':
    optional: true

  '@oxc-parser/binding-linux-riscv64-gnu@0.89.0':
    optional: true

  '@oxc-parser/binding-linux-s390x-gnu@0.89.0':
    optional: true

  '@oxc-parser/binding-linux-x64-gnu@0.89.0':
    optional: true

  '@oxc-parser/binding-linux-x64-musl@0.89.0':
    optional: true

  '@oxc-parser/binding-wasm32-wasi@0.89.0':
    dependencies:
      '@napi-rs/wasm-runtime': 1.0.5
    optional: true

  '@oxc-parser/binding-win32-arm64-msvc@0.89.0':
    optional: true

  '@oxc-parser/binding-win32-x64-msvc@0.89.0':
    optional: true

  '@oxc-project/runtime@0.78.0': {}

  '@oxc-project/types@0.78.0': {}

  '@oxc-project/types@0.89.0': {}

  '@oxc-transform/binding-android-arm64@0.89.0':
    optional: true

  '@oxc-transform/binding-darwin-arm64@0.89.0':
    optional: true

  '@oxc-transform/binding-darwin-x64@0.89.0':
    optional: true

  '@oxc-transform/binding-freebsd-x64@0.89.0':
    optional: true

  '@oxc-transform/binding-linux-arm-gnueabihf@0.89.0':
    optional: true

  '@oxc-transform/binding-linux-arm-musleabihf@0.89.0':
    optional: true

  '@oxc-transform/binding-linux-arm64-gnu@0.89.0':
    optional: true

  '@oxc-transform/binding-linux-arm64-musl@0.89.0':
    optional: true

  '@oxc-transform/binding-linux-riscv64-gnu@0.89.0':
    optional: true

  '@oxc-transform/binding-linux-s390x-gnu@0.89.0':
    optional: true

  '@oxc-transform/binding-linux-x64-gnu@0.89.0':
    optional: true

  '@oxc-transform/binding-linux-x64-musl@0.89.0':
    optional: true

  '@oxc-transform/binding-wasm32-wasi@0.89.0':
    dependencies:
      '@napi-rs/wasm-runtime': 1.0.5
    optional: true

  '@oxc-transform/binding-win32-arm64-msvc@0.89.0':
    optional: true

  '@oxc-transform/binding-win32-x64-msvc@0.89.0':
    optional: true

  '@rolldown/binding-android-arm64@1.0.0-beta.30':
    optional: true

  '@rolldown/binding-darwin-arm64@1.0.0-beta.30':
    optional: true

  '@rolldown/binding-darwin-x64@1.0.0-beta.30':
    optional: true

  '@rolldown/binding-freebsd-x64@1.0.0-beta.30':
    optional: true

  '@rolldown/binding-linux-arm-gnueabihf@1.0.0-beta.30':
    optional: true

  '@rolldown/binding-linux-arm64-gnu@1.0.0-beta.30':
    optional: true

  '@rolldown/binding-linux-arm64-musl@1.0.0-beta.30':
    optional: true

  '@rolldown/binding-linux-arm64-ohos@1.0.0-beta.30':
    optional: true

  '@rolldown/binding-linux-x64-gnu@1.0.0-beta.30':
    optional: true

  '@rolldown/binding-linux-x64-musl@1.0.0-beta.30':
    optional: true

  '@rolldown/binding-wasm32-wasi@1.0.0-beta.30':
    dependencies:
      '@napi-rs/wasm-runtime': 1.0.5
    optional: true

  '@rolldown/binding-win32-arm64-msvc@1.0.0-beta.30':
    optional: true

  '@rolldown/binding-win32-ia32-msvc@1.0.0-beta.30':
    optional: true

  '@rolldown/binding-win32-x64-msvc@1.0.0-beta.30':
    optional: true

  '@rolldown/pluginutils@1.0.0-beta.30': {}

  '@rolldown/pluginutils@1.0.0-beta.32': {}

  '@rollup/rollup-android-arm-eabi@4.49.0':
    optional: true

  '@rollup/rollup-android-arm64@4.49.0':
    optional: true

  '@rollup/rollup-darwin-arm64@4.49.0':
    optional: true

  '@rollup/rollup-darwin-x64@4.49.0':
    optional: true

  '@rollup/rollup-freebsd-arm64@4.49.0':
    optional: true

  '@rollup/rollup-freebsd-x64@4.49.0':
    optional: true

  '@rollup/rollup-linux-arm-gnueabihf@4.49.0':
    optional: true

  '@rollup/rollup-linux-arm-musleabihf@4.49.0':
    optional: true

  '@rollup/rollup-linux-arm64-gnu@4.49.0':
    optional: true

  '@rollup/rollup-linux-arm64-musl@4.49.0':
    optional: true

  '@rollup/rollup-linux-loongarch64-gnu@4.49.0':
    optional: true

  '@rollup/rollup-linux-ppc64-gnu@4.49.0':
    optional: true

  '@rollup/rollup-linux-riscv64-gnu@4.49.0':
    optional: true

  '@rollup/rollup-linux-riscv64-musl@4.49.0':
    optional: true

  '@rollup/rollup-linux-s390x-gnu@4.49.0':
    optional: true

  '@rollup/rollup-linux-x64-gnu@4.49.0':
    optional: true

  '@rollup/rollup-linux-x64-musl@4.49.0':
    optional: true

  '@rollup/rollup-win32-arm64-msvc@4.49.0':
    optional: true

  '@rollup/rollup-win32-ia32-msvc@4.49.0':
    optional: true

  '@rollup/rollup-win32-x64-msvc@4.49.0':
    optional: true

  '@tybys/wasm-util@0.10.1':
    dependencies:
      tslib: 2.8.1
    optional: true

  '@types/babel__core@7.20.5':
    dependencies:
      '@babel/parser': 7.28.3
      '@babel/types': 7.28.2
      '@types/babel__generator': 7.27.0
      '@types/babel__template': 7.4.4
      '@types/babel__traverse': 7.28.0

  '@types/babel__generator@7.27.0':
    dependencies:
      '@babel/types': 7.28.2

  '@types/babel__template@7.4.4':
    dependencies:
      '@babel/parser': 7.28.3
      '@babel/types': 7.28.2

  '@types/babel__traverse@7.28.0':
    dependencies:
      '@babel/types': 7.28.2

  '@types/estree@1.0.8': {}

  '@types/json-schema@7.0.15': {}

  '@types/node@18.19.123':
    dependencies:
      undici-types: 5.26.5

  '@types/react-dom@19.1.8(@types/react@19.1.11)':
    dependencies:
      '@types/react': 19.1.11

  '@types/react@19.1.11':
    dependencies:
      csstype: 3.1.3

  '@typescript-eslint/eslint-plugin@8.41.0(@typescript-eslint/parser@8.41.0(eslint@9.34.0(jiti@2.5.1))(typescript@5.8.3))(eslint@9.34.0(jiti@2.5.1))(typescript@5.8.3)':
    dependencies:
      '@eslint-community/regexpp': 4.12.1
      '@typescript-eslint/parser': 8.41.0(eslint@9.34.0(jiti@2.5.1))(typescript@5.8.3)
      '@typescript-eslint/scope-manager': 8.41.0
      '@typescript-eslint/type-utils': 8.41.0(eslint@9.34.0(jiti@2.5.1))(typescript@5.8.3)
      '@typescript-eslint/utils': 8.41.0(eslint@9.34.0(jiti@2.5.1))(typescript@5.8.3)
      '@typescript-eslint/visitor-keys': 8.41.0
      eslint: 9.34.0(jiti@2.5.1)
      graphemer: 1.4.0
      ignore: 7.0.5
      natural-compare: 1.4.0
      ts-api-utils: 2.1.0(typescript@5.8.3)
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/parser@8.41.0(eslint@9.34.0(jiti@2.5.1))(typescript@5.8.3)':
    dependencies:
      '@typescript-eslint/scope-manager': 8.41.0
      '@typescript-eslint/types': 8.41.0
      '@typescript-eslint/typescript-estree': 8.41.0(typescript@5.8.3)
      '@typescript-eslint/visitor-keys': 8.41.0
      debug: 4.4.1
      eslint: 9.34.0(jiti@2.5.1)
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/project-service@8.41.0(typescript@5.8.3)':
    dependencies:
      '@typescript-eslint/tsconfig-utils': 8.41.0(typescript@5.8.3)
      '@typescript-eslint/types': 8.41.0
      debug: 4.4.1
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/scope-manager@8.41.0':
    dependencies:
      '@typescript-eslint/types': 8.41.0
      '@typescript-eslint/visitor-keys': 8.41.0

  '@typescript-eslint/tsconfig-utils@8.41.0(typescript@5.8.3)':
    dependencies:
      typescript: 5.8.3

  '@typescript-eslint/type-utils@8.41.0(eslint@9.34.0(jiti@2.5.1))(typescript@5.8.3)':
    dependencies:
      '@typescript-eslint/types': 8.41.0
      '@typescript-eslint/typescript-estree': 8.41.0(typescript@5.8.3)
      '@typescript-eslint/utils': 8.41.0(eslint@9.34.0(jiti@2.5.1))(typescript@5.8.3)
      debug: 4.4.1
      eslint: 9.34.0(jiti@2.5.1)
      ts-api-utils: 2.1.0(typescript@5.8.3)
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/types@8.41.0': {}

  '@typescript-eslint/typescript-estree@8.41.0(typescript@5.8.3)':
    dependencies:
      '@typescript-eslint/project-service': 8.41.0(typescript@5.8.3)
      '@typescript-eslint/tsconfig-utils': 8.41.0(typescript@5.8.3)
      '@typescript-eslint/types': 8.41.0
      '@typescript-eslint/visitor-keys': 8.41.0
      debug: 4.4.1
      fast-glob: 3.3.3
      is-glob: 4.0.3
      minimatch: 9.0.5
      semver: 7.7.2
      ts-api-utils: 2.1.0(typescript@5.8.3)
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/utils@8.41.0(eslint@9.34.0(jiti@2.5.1))(typescript@5.8.3)':
    dependencies:
      '@eslint-community/eslint-utils': 4.7.0(eslint@9.34.0(jiti@2.5.1))
      '@typescript-eslint/scope-manager': 8.41.0
      '@typescript-eslint/types': 8.41.0
      '@typescript-eslint/typescript-estree': 8.41.0(typescript@5.8.3)
      eslint: 9.34.0(jiti@2.5.1)
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/visitor-keys@8.41.0':
    dependencies:
      '@typescript-eslint/types': 8.41.0
      eslint-visitor-keys: 4.2.1

  '@vitejs/plugin-react@5.0.1(vite@7.1.3(@types/node@18.19.123)(jiti@2.5.1)(yaml@2.8.1))':
    dependencies:
      '@babel/core': 7.28.3
      '@babel/plugin-transform-react-jsx-self': 7.27.1(@babel/core@7.28.3)
      '@babel/plugin-transform-react-jsx-source': 7.27.1(@babel/core@7.28.3)
      '@rolldown/pluginutils': 1.0.0-beta.32
      '@types/babel__core': 7.20.5
      react-refresh: 0.17.0
      vite: 7.1.3(@types/node@18.19.123)(jiti@2.5.1)(yaml@2.8.1)
    transitivePeerDependencies:
      - supports-color

  acorn-jsx@5.3.2(acorn@8.15.0):
    dependencies:
      acorn: 8.15.0

  acorn@8.15.0: {}

  ajv@6.12.6:
    dependencies:
      fast-deep-equal: 3.1.3
      fast-json-stable-stringify: 2.1.0
      json-schema-traverse: 0.4.1
      uri-js: 4.4.1

  ansi-styles@4.3.0:
    dependencies:
      color-convert: 2.0.1

  ansis@4.1.0: {}

  argparse@2.0.1: {}

  ast-kit@2.1.2:
    dependencies:
      '@babel/parser': 7.28.3
      pathe: 2.0.3

  async@3.2.6: {}

  balanced-match@1.0.2: {}

  basic-auth@2.0.1:
    dependencies:
      safe-buffer: 5.1.2

  birpc@2.5.0: {}

  brace-expansion@1.1.12:
    dependencies:
      balanced-match: 1.0.2
      concat-map: 0.0.1

  brace-expansion@2.0.2:
    dependencies:
      balanced-match: 1.0.2

  braces@3.0.3:
    dependencies:
      fill-range: 7.1.1

  browserslist@4.25.3:
    dependencies:
      caniuse-lite: 1.0.30001737
      electron-to-chromium: 1.5.209
      node-releases: 2.0.19
      update-browserslist-db: 1.1.3(browserslist@4.25.3)

  bundle-name@4.1.0:
    dependencies:
      run-applescript: 7.0.0

  c12@3.2.0:
    dependencies:
      chokidar: 4.0.3
      confbox: 0.2.2
      defu: 6.1.4
      dotenv: 17.2.1
      exsolve: 1.0.7
      giget: 2.0.0
      jiti: 2.5.1
      ohash: 2.0.11
      pathe: 2.0.3
      perfect-debounce: 1.0.0
      pkg-types: 2.3.0
      rc9: 2.1.2

  call-bind-apply-helpers@1.0.2:
    dependencies:
      es-errors: 1.3.0
      function-bind: 1.1.2

  call-bound@1.0.4:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      get-intrinsic: 1.3.0

  callsites@3.1.0: {}

  caniuse-lite@1.0.30001737: {}

  chalk@4.1.2:
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0

  changelogen@0.6.2:
    dependencies:
      c12: 3.2.0
      confbox: 0.2.2
      consola: 3.4.2
      convert-gitmoji: 0.1.5
      mri: 1.2.0
      node-fetch-native: 1.6.7
      ofetch: 1.4.1
      open: 10.2.0
      pathe: 2.0.3
      pkg-types: 2.3.0
      scule: 1.3.0
      semver: 7.7.2
      std-env: 3.9.0
    transitivePeerDependencies:
      - magicast

  chokidar@4.0.3:
    dependencies:
      readdirp: 4.1.2

  citty@0.1.6:
    dependencies:
      consola: 3.4.2

  color-convert@2.0.1:
    dependencies:
      color-name: 1.1.4

  color-name@1.1.4: {}

  concat-map@0.0.1: {}

  confbox@0.2.2: {}

  consola@3.4.2: {}

  convert-gitmoji@0.1.5: {}

  convert-source-map@2.0.0: {}

  corser@2.0.1: {}

  cross-spawn@7.0.6:
    dependencies:
      path-key: 3.1.1
      shebang-command: 2.0.0
      which: 2.0.2

  csstype@3.1.3: {}

  debounce@1.2.1: {}

  debug@4.4.1:
    dependencies:
      ms: 2.1.3

  deep-is@0.1.4: {}

  default-browser-id@5.0.0: {}

  default-browser@5.2.1:
    dependencies:
      bundle-name: 4.1.0
      default-browser-id: 5.0.0

  define-lazy-prop@3.0.0: {}

  defu@6.1.4: {}

  destr@2.0.5: {}

  dotenv@17.2.1: {}

  dts-resolver@2.1.2: {}

  dunder-proto@1.0.1:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-errors: 1.3.0
      gopd: 1.2.0

  electron-to-chromium@1.5.209: {}

  es-define-property@1.0.1: {}

  es-errors@1.3.0: {}

  es-object-atoms@1.1.1:
    dependencies:
      es-errors: 1.3.0

  esbuild@0.25.9:
    optionalDependencies:
      '@esbuild/aix-ppc64': 0.25.9
      '@esbuild/android-arm': 0.25.9
      '@esbuild/android-arm64': 0.25.9
      '@esbuild/android-x64': 0.25.9
      '@esbuild/darwin-arm64': 0.25.9
      '@esbuild/darwin-x64': 0.25.9
      '@esbuild/freebsd-arm64': 0.25.9
      '@esbuild/freebsd-x64': 0.25.9
      '@esbuild/linux-arm': 0.25.9
      '@esbuild/linux-arm64': 0.25.9
      '@esbuild/linux-ia32': 0.25.9
      '@esbuild/linux-loong64': 0.25.9
      '@esbuild/linux-mips64el': 0.25.9
      '@esbuild/linux-ppc64': 0.25.9
      '@esbuild/linux-riscv64': 0.25.9
      '@esbuild/linux-s390x': 0.25.9
      '@esbuild/linux-x64': 0.25.9
      '@esbuild/netbsd-arm64': 0.25.9
      '@esbuild/netbsd-x64': 0.25.9
      '@esbuild/openbsd-arm64': 0.25.9
      '@esbuild/openbsd-x64': 0.25.9
      '@esbuild/openharmony-arm64': 0.25.9
      '@esbuild/sunos-x64': 0.25.9
      '@esbuild/win32-arm64': 0.25.9
      '@esbuild/win32-ia32': 0.25.9
      '@esbuild/win32-x64': 0.25.9

  escalade@3.2.0: {}

  escape-string-regexp@4.0.0: {}

  eslint-plugin-react-hooks@5.2.0(eslint@9.34.0(jiti@2.5.1)):
    dependencies:
      eslint: 9.34.0(jiti@2.5.1)

  eslint-plugin-react-refresh@0.4.20(eslint@9.34.0(jiti@2.5.1)):
    dependencies:
      eslint: 9.34.0(jiti@2.5.1)

  eslint-scope@8.4.0:
    dependencies:
      esrecurse: 4.3.0
      estraverse: 5.3.0

  eslint-visitor-keys@3.4.3: {}

  eslint-visitor-keys@4.2.1: {}

  eslint@9.34.0(jiti@2.5.1):
    dependencies:
      '@eslint-community/eslint-utils': 4.7.0(eslint@9.34.0(jiti@2.5.1))
      '@eslint-community/regexpp': 4.12.1
      '@eslint/config-array': 0.21.0
      '@eslint/config-helpers': 0.3.1
      '@eslint/core': 0.15.2
      '@eslint/eslintrc': 3.3.1
      '@eslint/js': 9.34.0
      '@eslint/plugin-kit': 0.3.5
      '@humanfs/node': 0.16.6
      '@humanwhocodes/module-importer': 1.0.1
      '@humanwhocodes/retry': 0.4.3
      '@types/estree': 1.0.8
      '@types/json-schema': 7.0.15
      ajv: 6.12.6
      chalk: 4.1.2
      cross-spawn: 7.0.6
      debug: 4.4.1
      escape-string-regexp: 4.0.0
      eslint-scope: 8.4.0
      eslint-visitor-keys: 4.2.1
      espree: 10.4.0
      esquery: 1.6.0
      esutils: 2.0.3
      fast-deep-equal: 3.1.3
      file-entry-cache: 8.0.0
      find-up: 5.0.0
      glob-parent: 6.0.2
      ignore: 5.3.2
      imurmurhash: 0.1.4
      is-glob: 4.0.3
      json-stable-stringify-without-jsonify: 1.0.1
      lodash.merge: 4.6.2
      minimatch: 3.1.2
      natural-compare: 1.4.0
      optionator: 0.9.4
    optionalDependencies:
      jiti: 2.5.1
    transitivePeerDependencies:
      - supports-color

  espree@10.4.0:
    dependencies:
      acorn: 8.15.0
      acorn-jsx: 5.3.2(acorn@8.15.0)
      eslint-visitor-keys: 4.2.1

  esquery@1.6.0:
    dependencies:
      estraverse: 5.3.0

  esrecurse@4.3.0:
    dependencies:
      estraverse: 5.3.0

  estraverse@5.3.0: {}

  esutils@2.0.3: {}

  eventemitter3@4.0.7: {}

  exsolve@1.0.7: {}

  fast-deep-equal@3.1.3: {}

  fast-glob@3.3.3:
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      '@nodelib/fs.walk': 1.2.8
      glob-parent: 5.1.2
      merge2: 1.4.1
      micromatch: 4.0.8

  fast-json-stable-stringify@2.1.0: {}

  fast-levenshtein@2.0.6: {}

  fastq@1.19.1:
    dependencies:
      reusify: 1.1.0

  fdir@6.5.0(picomatch@4.0.3):
    optionalDependencies:
      picomatch: 4.0.3

  file-entry-cache@8.0.0:
    dependencies:
      flat-cache: 4.0.1

  fill-range@7.1.1:
    dependencies:
      to-regex-range: 5.0.1

  find-up@5.0.0:
    dependencies:
      locate-path: 6.0.0
      path-exists: 4.0.0

  flat-cache@4.0.1:
    dependencies:
      flatted: 3.3.3
      keyv: 4.5.4

  flatted@3.3.3: {}

  follow-redirects@1.15.11: {}

  fsevents@2.3.3:
    optional: true

  function-bind@1.1.2: {}

  gensync@1.0.0-beta.2: {}

  get-intrinsic@1.3.0:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-define-property: 1.0.1
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      function-bind: 1.1.2
      get-proto: 1.0.1
      gopd: 1.2.0
      has-symbols: 1.1.0
      hasown: 2.0.2
      math-intrinsics: 1.1.0

  get-proto@1.0.1:
    dependencies:
      dunder-proto: 1.0.1
      es-object-atoms: 1.1.1

  get-tsconfig@4.10.1:
    dependencies:
      resolve-pkg-maps: 1.0.0

  giget@2.0.0:
    dependencies:
      citty: 0.1.6
      consola: 3.4.2
      defu: 6.1.4
      node-fetch-native: 1.6.7
      nypm: 0.6.1
      pathe: 2.0.3

  git-cz@4.9.0: {}

  glob-parent@5.1.2:
    dependencies:
      is-glob: 4.0.3

  glob-parent@6.0.2:
    dependencies:
      is-glob: 4.0.3

  globals@14.0.0: {}

  globals@16.3.0: {}

  gopd@1.2.0: {}

  graphemer@1.4.0: {}

  has-flag@4.0.0: {}

  has-passive-events@1.0.0:
    dependencies:
      is-browser: 2.1.0

  has-symbols@1.1.0: {}

  hasown@2.0.2:
    dependencies:
      function-bind: 1.1.2

  he@1.2.0: {}

  html-encoding-sniffer@3.0.0:
    dependencies:
      whatwg-encoding: 2.0.0

  http-proxy@1.18.1:
    dependencies:
      eventemitter3: 4.0.7
      follow-redirects: 1.15.11
      requires-port: 1.0.0
    transitivePeerDependencies:
      - debug

  http-server@14.1.1:
    dependencies:
      basic-auth: 2.0.1
      chalk: 4.1.2
      corser: 2.0.1
      he: 1.2.0
      html-encoding-sniffer: 3.0.0
      http-proxy: 1.18.1
      mime: 1.6.0
      minimist: 1.2.8
      opener: 1.5.2
      portfinder: 1.0.37
      secure-compare: 3.0.1
      union: 0.5.0
      url-join: 4.0.1
    transitivePeerDependencies:
      - debug
      - supports-color

  huse@2.0.4(react@19.1.1):
    dependencies:
      '@huse/action-pending': 1.1.2(react@19.1.1)
      '@huse/boolean': 1.2.0(react@19.1.1)
      '@huse/click-outside': 1.1.1(react@19.1.1)
      '@huse/collection': 1.1.2(react@19.1.1)
      '@huse/debounce': 1.1.2(react@19.1.1)
      '@huse/debug': 1.1.1(react@19.1.1)
      '@huse/derived-state': 1.1.1(react@19.1.1)
      '@huse/document-event': 1.1.1(react@19.1.1)
      '@huse/document-title': 1.1.1(react@19.1.1)
      '@huse/effect-ref': 1.1.1(react@19.1.1)
      '@huse/element-size': 1.1.1(react@19.1.1)
      '@huse/hover': 1.1.3(react@19.1.1)
      '@huse/infinite-scroll': 1.2.2(react@19.1.1)
      '@huse/input-value': 1.1.1(react@19.1.1)
      '@huse/intersection': 1.2.1(react@19.1.1)
      '@huse/local-storage': 1.1.1(react@19.1.1)
      '@huse/media': 1.1.1(react@19.1.1)
      '@huse/merged-ref': 1.2.1(react@19.1.1)
      '@huse/methods': 1.2.2(react@19.1.1)
      '@huse/network': 1.1.3(react@19.1.1)
      '@huse/number': 1.2.2(react@19.1.1)
      '@huse/optimistic': 0.10.1(react@19.1.1)
      '@huse/performance': 1.1.1(react@19.1.1)
      '@huse/poll': 1.1.2(react@19.1.1)
      '@huse/previous-value': 1.1.1(react@19.1.1)
      '@huse/request': 1.3.0(react@19.1.1)
      '@huse/script': 1.1.1(react@19.1.1)
      '@huse/scroll-into-view': 1.1.1(react@19.1.1)
      '@huse/scroll-lock': 1.1.1(react@19.1.1)
      '@huse/scroll-position': 1.1.1(react@19.1.1)
      '@huse/selection': 1.1.2(react@19.1.1)
      '@huse/snapshot': 1.1.2(react@19.1.1)
      '@huse/timeout': 1.1.1(react@19.1.1)
      '@huse/transition-state': 1.1.1(react@19.1.1)
      '@huse/update': 1.1.1(react@19.1.1)
      '@huse/user-media': 0.10.1(react@19.1.1)
      '@huse/web-socket': 0.11.1(react@19.1.1)
      '@huse/window-size': 1.1.1(react@19.1.1)
      react: 19.1.1

  husky@8.0.3: {}

  iconv-lite@0.6.3:
    dependencies:
      safer-buffer: 2.1.2

  ignore@5.3.2: {}

  ignore@7.0.5: {}

  immer@9.0.21: {}

  import-fresh@3.3.1:
    dependencies:
      parent-module: 1.0.1
      resolve-from: 4.0.0

  imurmurhash@0.1.4: {}

  is-browser@2.1.0: {}

  is-docker@3.0.0: {}

  is-extglob@2.1.1: {}

  is-glob@4.0.3:
    dependencies:
      is-extglob: 2.1.1

  is-inside-container@1.0.0:
    dependencies:
      is-docker: 3.0.0

  is-number@7.0.0: {}

  is-wsl@3.1.0:
    dependencies:
      is-inside-container: 1.0.0

  isexe@2.0.0: {}

  jiti@2.5.1: {}

  js-tokens@4.0.0: {}

  js-yaml@4.1.0:
    dependencies:
      argparse: 2.0.1

  jsesc@3.1.0: {}

  json-buffer@3.0.1: {}

  json-schema-traverse@0.4.1: {}

  json-stable-stringify-without-jsonify@1.0.1: {}

  json5@2.2.3: {}

  keyv@4.5.4:
    dependencies:
      json-buffer: 3.0.1

  levn@0.4.1:
    dependencies:
      prelude-ls: 1.2.1
      type-check: 0.4.0

  locate-path@6.0.0:
    dependencies:
      p-locate: 5.0.0

  lodash.merge@4.6.2: {}

  lodash@4.17.21: {}

  lru-cache@5.1.1:
    dependencies:
      yallist: 3.1.1

  magic-string@0.30.19:
    dependencies:
      '@jridgewell/sourcemap-codec': 1.5.5

  math-intrinsics@1.1.0: {}

  merge2@1.4.1: {}

  micromatch@4.0.8:
    dependencies:
      braces: 3.0.3
      picomatch: 2.3.1

  mime@1.6.0: {}

  minimatch@3.1.2:
    dependencies:
      brace-expansion: 1.1.12

  minimatch@9.0.5:
    dependencies:
      brace-expansion: 2.0.2

  minimist@1.2.8: {}

  mri@1.2.0: {}

  ms@2.1.3: {}

  nanoid@3.3.11: {}

  natural-compare@1.4.0: {}

  node-fetch-native@1.6.7: {}

  node-releases@2.0.19: {}

  nypm@0.6.1:
    dependencies:
      citty: 0.1.6
      consola: 3.4.2
      pathe: 2.0.3
      pkg-types: 2.3.0
      tinyexec: 1.0.1

  object-inspect@1.13.4: {}

  ofetch@1.4.1:
    dependencies:
      destr: 2.0.5
      node-fetch-native: 1.6.7
      ufo: 1.6.1

  ohash@2.0.11: {}

  open@10.2.0:
    dependencies:
      default-browser: 5.2.1
      define-lazy-prop: 3.0.0
      is-inside-container: 1.0.0
      wsl-utils: 0.1.0

  opener@1.5.2: {}

  optionator@0.9.4:
    dependencies:
      deep-is: 0.1.4
      fast-levenshtein: 2.0.6
      levn: 0.4.1
      prelude-ls: 1.2.1
      type-check: 0.4.0
      word-wrap: 1.2.5

  oxc-minify@0.89.0:
    optionalDependencies:
      '@oxc-minify/binding-android-arm64': 0.89.0
      '@oxc-minify/binding-darwin-arm64': 0.89.0
      '@oxc-minify/binding-darwin-x64': 0.89.0
      '@oxc-minify/binding-freebsd-x64': 0.89.0
      '@oxc-minify/binding-linux-arm-gnueabihf': 0.89.0
      '@oxc-minify/binding-linux-arm-musleabihf': 0.89.0
      '@oxc-minify/binding-linux-arm64-gnu': 0.89.0
      '@oxc-minify/binding-linux-arm64-musl': 0.89.0
      '@oxc-minify/binding-linux-riscv64-gnu': 0.89.0
      '@oxc-minify/binding-linux-s390x-gnu': 0.89.0
      '@oxc-minify/binding-linux-x64-gnu': 0.89.0
      '@oxc-minify/binding-linux-x64-musl': 0.89.0
      '@oxc-minify/binding-wasm32-wasi': 0.89.0
      '@oxc-minify/binding-win32-arm64-msvc': 0.89.0
      '@oxc-minify/binding-win32-x64-msvc': 0.89.0

  oxc-parser@0.89.0:
    dependencies:
      '@oxc-project/types': 0.89.0
    optionalDependencies:
      '@oxc-parser/binding-android-arm64': 0.89.0
      '@oxc-parser/binding-darwin-arm64': 0.89.0
      '@oxc-parser/binding-darwin-x64': 0.89.0
      '@oxc-parser/binding-freebsd-x64': 0.89.0
      '@oxc-parser/binding-linux-arm-gnueabihf': 0.89.0
      '@oxc-parser/binding-linux-arm-musleabihf': 0.89.0
      '@oxc-parser/binding-linux-arm64-gnu': 0.89.0
      '@oxc-parser/binding-linux-arm64-musl': 0.89.0
      '@oxc-parser/binding-linux-riscv64-gnu': 0.89.0
      '@oxc-parser/binding-linux-s390x-gnu': 0.89.0
      '@oxc-parser/binding-linux-x64-gnu': 0.89.0
      '@oxc-parser/binding-linux-x64-musl': 0.89.0
      '@oxc-parser/binding-wasm32-wasi': 0.89.0
      '@oxc-parser/binding-win32-arm64-msvc': 0.89.0
      '@oxc-parser/binding-win32-x64-msvc': 0.89.0

  oxc-transform@0.89.0:
    optionalDependencies:
      '@oxc-transform/binding-android-arm64': 0.89.0
      '@oxc-transform/binding-darwin-arm64': 0.89.0
      '@oxc-transform/binding-darwin-x64': 0.89.0
      '@oxc-transform/binding-freebsd-x64': 0.89.0
      '@oxc-transform/binding-linux-arm-gnueabihf': 0.89.0
      '@oxc-transform/binding-linux-arm-musleabihf': 0.89.0
      '@oxc-transform/binding-linux-arm64-gnu': 0.89.0
      '@oxc-transform/binding-linux-arm64-musl': 0.89.0
      '@oxc-transform/binding-linux-riscv64-gnu': 0.89.0
      '@oxc-transform/binding-linux-s390x-gnu': 0.89.0
      '@oxc-transform/binding-linux-x64-gnu': 0.89.0
      '@oxc-transform/binding-linux-x64-musl': 0.89.0
      '@oxc-transform/binding-wasm32-wasi': 0.89.0
      '@oxc-transform/binding-win32-arm64-msvc': 0.89.0
      '@oxc-transform/binding-win32-x64-msvc': 0.89.0

  p-limit@3.1.0:
    dependencies:
      yocto-queue: 0.1.0

  p-locate@5.0.0:
    dependencies:
      p-limit: 3.1.0

  parent-module@1.0.1:
    dependencies:
      callsites: 3.1.0

  path-exists@4.0.0: {}

  path-key@3.1.1: {}

  pathe@2.0.3: {}

  perfect-debounce@1.0.0: {}

  picocolors@1.1.1: {}

  picomatch@2.3.1: {}

  picomatch@4.0.3: {}

  pkg-types@2.3.0:
    dependencies:
      confbox: 0.2.2
      exsolve: 1.0.7
      pathe: 2.0.3

  portfinder@1.0.37:
    dependencies:
      async: 3.2.6
      debug: 4.4.1
    transitivePeerDependencies:
      - supports-color

  postcss@8.5.6:
    dependencies:
      nanoid: 3.3.11
      picocolors: 1.1.1
      source-map-js: 1.2.1

  prelude-ls@1.2.1: {}

  pretty-bytes@7.0.1: {}

  punycode@2.3.1: {}

  qs@6.14.0:
    dependencies:
      side-channel: 1.1.0

  query-shape@1.0.2: {}

  queue-microtask@1.2.3: {}

  rc9@2.1.2:
    dependencies:
      defu: 6.1.4
      destr: 2.0.5

  react-dom@19.1.1(react@19.1.1):
    dependencies:
      react: 19.1.1
      scheduler: 0.26.0

  react-refresh@0.17.0: {}

  react@19.1.1: {}

  readdirp@4.1.2: {}

  requires-port@1.0.0: {}

  resize-detector@0.3.0: {}

  resolve-from@4.0.0: {}

  resolve-pkg-maps@1.0.0: {}

  reusify@1.1.0: {}

  robuild@0.0.5:
    dependencies:
      c12: 3.2.0
      consola: 3.4.2
      defu: 6.1.4
      exsolve: 1.0.7
      magic-string: 0.30.19
      oxc-minify: 0.89.0
      oxc-parser: 0.89.0
      oxc-transform: 0.89.0
      pretty-bytes: 7.0.1
      rolldown: 1.0.0-beta.30
      rolldown-plugin-dts: 0.16.5(rolldown@1.0.0-beta.30)(typescript@5.8.3)
      tinyglobby: 0.2.14
      typescript: 5.8.3
    transitivePeerDependencies:
      - '@ts-macro/tsc'
      - '@typescript/native-preview'
      - magicast
      - oxc-resolver
      - supports-color
      - vue-tsc

  rolldown-plugin-dts@0.16.5(rolldown@1.0.0-beta.30)(typescript@5.8.3):
    dependencies:
      '@babel/generator': 7.28.3
      '@babel/parser': 7.28.4
      '@babel/types': 7.28.4
      ast-kit: 2.1.2
      birpc: 2.5.0
      debug: 4.4.1
      dts-resolver: 2.1.2
      get-tsconfig: 4.10.1
      magic-string: 0.30.19
      rolldown: 1.0.0-beta.30
    optionalDependencies:
      typescript: 5.8.3
    transitivePeerDependencies:
      - oxc-resolver
      - supports-color

  rolldown@1.0.0-beta.30:
    dependencies:
      '@oxc-project/runtime': 0.78.0
      '@oxc-project/types': 0.78.0
      '@rolldown/pluginutils': 1.0.0-beta.30
      ansis: 4.1.0
    optionalDependencies:
      '@rolldown/binding-android-arm64': 1.0.0-beta.30
      '@rolldown/binding-darwin-arm64': 1.0.0-beta.30
      '@rolldown/binding-darwin-x64': 1.0.0-beta.30
      '@rolldown/binding-freebsd-x64': 1.0.0-beta.30
      '@rolldown/binding-linux-arm-gnueabihf': 1.0.0-beta.30
      '@rolldown/binding-linux-arm64-gnu': 1.0.0-beta.30
      '@rolldown/binding-linux-arm64-musl': 1.0.0-beta.30
      '@rolldown/binding-linux-arm64-ohos': 1.0.0-beta.30
      '@rolldown/binding-linux-x64-gnu': 1.0.0-beta.30
      '@rolldown/binding-linux-x64-musl': 1.0.0-beta.30
      '@rolldown/binding-wasm32-wasi': 1.0.0-beta.30
      '@rolldown/binding-win32-arm64-msvc': 1.0.0-beta.30
      '@rolldown/binding-win32-ia32-msvc': 1.0.0-beta.30
      '@rolldown/binding-win32-x64-msvc': 1.0.0-beta.30

  rollup@4.49.0:
    dependencies:
      '@types/estree': 1.0.8
    optionalDependencies:
      '@rollup/rollup-android-arm-eabi': 4.49.0
      '@rollup/rollup-android-arm64': 4.49.0
      '@rollup/rollup-darwin-arm64': 4.49.0
      '@rollup/rollup-darwin-x64': 4.49.0
      '@rollup/rollup-freebsd-arm64': 4.49.0
      '@rollup/rollup-freebsd-x64': 4.49.0
      '@rollup/rollup-linux-arm-gnueabihf': 4.49.0
      '@rollup/rollup-linux-arm-musleabihf': 4.49.0
      '@rollup/rollup-linux-arm64-gnu': 4.49.0
      '@rollup/rollup-linux-arm64-musl': 4.49.0
      '@rollup/rollup-linux-loongarch64-gnu': 4.49.0
      '@rollup/rollup-linux-ppc64-gnu': 4.49.0
      '@rollup/rollup-linux-riscv64-gnu': 4.49.0
      '@rollup/rollup-linux-riscv64-musl': 4.49.0
      '@rollup/rollup-linux-s390x-gnu': 4.49.0
      '@rollup/rollup-linux-x64-gnu': 4.49.0
      '@rollup/rollup-linux-x64-musl': 4.49.0
      '@rollup/rollup-win32-arm64-msvc': 4.49.0
      '@rollup/rollup-win32-ia32-msvc': 4.49.0
      '@rollup/rollup-win32-x64-msvc': 4.49.0
      fsevents: 2.3.3

  run-applescript@7.0.0: {}

  run-parallel@1.2.0:
    dependencies:
      queue-microtask: 1.2.3

  safe-buffer@5.1.2: {}

  safer-buffer@2.1.2: {}

  scheduler@0.26.0: {}

  scule@1.3.0: {}

  secure-compare@3.0.1: {}

  semver@6.3.1: {}

  semver@7.7.2: {}

  shallowequal@1.1.0: {}

  shebang-command@2.0.0:
    dependencies:
      shebang-regex: 3.0.0

  shebang-regex@3.0.0: {}

  side-channel-list@1.0.0:
    dependencies:
      es-errors: 1.3.0
      object-inspect: 1.13.4

  side-channel-map@1.0.1:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      object-inspect: 1.13.4

  side-channel-weakmap@1.0.2:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      object-inspect: 1.13.4
      side-channel-map: 1.0.1

  side-channel@1.1.0:
    dependencies:
      es-errors: 1.3.0
      object-inspect: 1.13.4
      side-channel-list: 1.0.0
      side-channel-map: 1.0.1
      side-channel-weakmap: 1.0.2

  source-map-js@1.2.1: {}

  std-env@3.9.0: {}

  strip-json-comments@3.1.1: {}

  supports-color@7.2.0:
    dependencies:
      has-flag: 4.0.0

  tinyexec@1.0.1: {}

  tinyglobby@0.2.14:
    dependencies:
      fdir: 6.5.0(picomatch@4.0.3)
      picomatch: 4.0.3

  to-regex-range@5.0.1:
    dependencies:
      is-number: 7.0.0

  ts-api-utils@2.1.0(typescript@5.8.3):
    dependencies:
      typescript: 5.8.3

  tslib@2.8.1:
    optional: true

  turbo-darwin-64@2.5.6:
    optional: true

  turbo-darwin-arm64@2.5.6:
    optional: true

  turbo-linux-64@2.5.6:
    optional: true

  turbo-linux-arm64@2.5.6:
    optional: true

  turbo-windows-64@2.5.6:
    optional: true

  turbo-windows-arm64@2.5.6:
    optional: true

  turbo@2.5.6:
    optionalDependencies:
      turbo-darwin-64: 2.5.6
      turbo-darwin-arm64: 2.5.6
      turbo-linux-64: 2.5.6
      turbo-linux-arm64: 2.5.6
      turbo-windows-64: 2.5.6
      turbo-windows-arm64: 2.5.6

  type-check@0.4.0:
    dependencies:
      prelude-ls: 1.2.1

  typescript-eslint@8.41.0(eslint@9.34.0(jiti@2.5.1))(typescript@5.8.3):
    dependencies:
      '@typescript-eslint/eslint-plugin': 8.41.0(@typescript-eslint/parser@8.41.0(eslint@9.34.0(jiti@2.5.1))(typescript@5.8.3))(eslint@9.34.0(jiti@2.5.1))(typescript@5.8.3)
      '@typescript-eslint/parser': 8.41.0(eslint@9.34.0(jiti@2.5.1))(typescript@5.8.3)
      '@typescript-eslint/typescript-estree': 8.41.0(typescript@5.8.3)
      '@typescript-eslint/utils': 8.41.0(eslint@9.34.0(jiti@2.5.1))(typescript@5.8.3)
      eslint: 9.34.0(jiti@2.5.1)
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color

  typescript@5.8.3: {}

  ufo@1.6.1: {}

  undici-types@5.26.5: {}

  union@0.5.0:
    dependencies:
      qs: 6.14.0

  update-browserslist-db@1.1.3(browserslist@4.25.3):
    dependencies:
      browserslist: 4.25.3
      escalade: 3.2.0
      picocolors: 1.1.1

  uri-js@4.4.1:
    dependencies:
      punycode: 2.3.1

  url-join@4.0.1: {}

  use-immer@0.6.0(immer@9.0.21)(react@19.1.1):
    dependencies:
      immer: 9.0.21
      react: 19.1.1

  user-attention@1.0.3:
    dependencies:
      has-passive-events: 1.0.0

  vite@7.1.3(@types/node@18.19.123)(jiti@2.5.1)(yaml@2.8.1):
    dependencies:
      esbuild: 0.25.9
      fdir: 6.5.0(picomatch@4.0.3)
      picomatch: 4.0.3
      postcss: 8.5.6
      rollup: 4.49.0
      tinyglobby: 0.2.14
    optionalDependencies:
      '@types/node': 18.19.123
      fsevents: 2.3.3
      jiti: 2.5.1
      yaml: 2.8.1

  whatwg-encoding@2.0.0:
    dependencies:
      iconv-lite: 0.6.3

  which@2.0.2:
    dependencies:
      isexe: 2.0.0

  word-wrap@1.2.5: {}

  wsl-utils@0.1.0:
    dependencies:
      is-wsl: 3.1.0

  yallist@3.1.1: {}

  yaml@2.8.1:
    optional: true

  yocto-queue@0.1.0: {}
