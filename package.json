{"name": "browser-storage-lru-cleaner", "version": "0.0.21", "description": "A TypeScript SDK for automatic browser storage cleanup using LRU algorithm", "main": "dist/index.mjs", "types": "dist/index.d.mts", "packageManager": "pnpm@10.9.0", "scripts": {"sdk:dev": "robuild --stub", "dev": "turbo run sdk:dev playground:dev --parallel", "build": "robuild", "prepublish": "pnpm build", "release": "changelogen --release && pnpm publish", "commit": "git-cz", "prepare": "husky install", "playground:dev": "cd playground && pnpm dev", "playground:build": "pnpm build && cd playground && pnpm build", "playground:preview": "./preview.sh", "deploy": "./deploy.sh", "deploy:simple": "./deploy-simple.sh"}, "keywords": ["localStorage", "indexedDB", "LRU", "cache", "storage", "browser", "cleanup"], "author": "Sunny-117", "license": "MIT", "devDependencies": {"@types/node": "^18.15.0", "changelogen": "^0.6.2", "git-cz": "^4.9.0", "http-server": "^14.1.1", "husky": "^8.0.0", "robuild": "^0.0.5", "turbo": "^2.5.6", "typescript": "^5.0.0"}, "files": ["dist/**/*"], "config": {"commitizen": {"path": "git-cz"}}, "homepage": "https://github.com/Sunny-117/browser-storage-lru-cleaner#readme", "repository": {"type": "git", "url": "git+https://github.com/Sunny-117/browser-storage-lru-cleaner.git"}, "git-cz": {"maxHeaderWidth": 100, "maxLineWidth": 100, "defaultType": "", "defaultScope": "", "defaultSubject": "", "defaultBody": "", "defaultIssues": "", "types": {"feat": {"description": "新功能 (A new feature)", "title": "Features"}, "fix": {"description": "修复 (A bug fix)", "title": "Bug Fixes"}, "docs": {"description": "文档 (Documentation only changes)", "title": "Documentation"}, "style": {"description": "格式 (Changes that do not affect the meaning of the code)", "title": "Styles"}, "refactor": {"description": "重构 (A code change that neither fixes a bug nor adds a feature)", "title": "Code Refactoring"}, "perf": {"description": "性能优化 (A code change that improves performance)", "title": "Performance Improvements"}, "test": {"description": "测试 (Adding missing tests or correcting existing tests)", "title": "Tests"}, "build": {"description": "构建 (Changes that affect the build system or external dependencies)", "title": "Builds"}, "ci": {"description": "CI/CD (Changes to our CI configuration files and scripts)", "title": "Continuous Integrations"}, "chore": {"description": "其他 (Other changes that don't modify src or test files)", "title": "Chores"}, "revert": {"description": "回滚 (Reverts a previous commit)", "title": "Reverts"}}}}