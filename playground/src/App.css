/* 全局样式 */
.app {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
}

.app-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
}

.app-header h1 {
  margin: 0 0 10px 0;
  font-size: 2.5rem;
}

.app-header p {
  margin: 0;
  opacity: 0.9;
  font-size: 1.1rem;
}

/* 主要内容区域 */
.main-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

/* 面板通用样式 */
.panel-section {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.panel-section h3 {
  margin: 0 0 20px 0;
  color: #343a40;
  font-size: 1.3rem;
}

.panel-section h4 {
  margin: 15px 0 10px 0;
  color: #495057;
  font-size: 1rem;
}

/* 控制面板 */
.control-group {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e9ecef;
}

.control-group:last-child {
  border-bottom: none;
}

.button-group {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

/* 按钮样式 */
button {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.primary {
  background: #007bff;
  color: white;
}

.primary:hover:not(:disabled) {
  background: #0056b3;
}

.secondary {
  background: #6c757d;
  color: white;
}

.secondary:hover:not(:disabled) {
  background: #545b62;
}

.success {
  background: #28a745;
  color: white;
}

.warning {
  background: #ffc107;
  color: #212529;
}

.warning:hover:not(:disabled) {
  background: #e0a800;
}

.danger {
  background: #dc3545;
  color: white;
}

.danger:hover:not(:disabled) {
  background: #c82333;
}

/* 输入组 */
.input-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.input-group input[type="text"] {
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
}

.size-control {
  display: flex;
  align-items: center;
  gap: 10px;
}

.size-control label {
  font-weight: 500;
  min-width: 80px;
}

.size-control input[type="range"] {
  flex: 1;
}

/* 统计面板 */
.usage-bar {
  margin-bottom: 20px;
}

.usage-label {
  font-weight: 600;
  margin-bottom: 8px;
  color: #495057;
}

.progress-bar {
  width: 100%;
  height: 24px;
  background: #e9ecef;
  border-radius: 12px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #28a745, #17a2b8);
  transition: width 0.3s ease;
  border-radius: 12px;
}

.progress-fill.warning {
  background: linear-gradient(90deg, #ffc107, #fd7e14);
}

.progress-fill.danger {
  background: linear-gradient(90deg, #dc3545, #e83e8c);
}

.usage-text {
  font-size: 14px;
  color: #6c757d;
  text-align: center;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.stat-label {
  font-weight: 500;
  color: #495057;
}

.stat-value {
  font-weight: 600;
  color: #007bff;
}

/* 存储项面板 */
.storage-panel {
  grid-column: 1 / -1;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #6c757d;
}

.empty-state p {
  margin: 5px 0;
}

.storage-items {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #dee2e6;
  border-radius: 6px;
}

.storage-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e9ecef;
  background: white;
  transition: background-color 0.2s ease;
}

.storage-item:hover {
  background: #f8f9fa;
}

.storage-item:last-child {
  border-bottom: none;
}

.item-info {
  flex: 1;
  min-width: 0;
}

.item-key {
  font-weight: 600;
  color: #343a40;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.system-tag {
  background: #6c757d;
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  font-weight: 500;
}

.item-details {
  display: flex;
  gap: 15px;
  font-size: 14px;
}

.item-size {
  color: #007bff;
  font-weight: 500;
}

.item-value {
  color: #6c757d;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.item-actions {
  display: flex;
  gap: 8px;
}

.access-btn, .delete-btn {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  padding: 0;
}

.access-btn {
  background: #17a2b8;
  color: white;
}

.access-btn:hover {
  background: #138496;
}

.delete-btn {
  background: #dc3545;
  color: white;
}

.delete-btn:hover {
  background: #c82333;
}

/* 日志面板 */
.logs-panel {
  grid-column: 1 / -1;
}

.logs-container {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 15px;
  height: 200px;
  overflow-y: auto;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.4;
}

.empty-logs {
  color: #6c757d;
  text-align: center;
  padding: 20px;
}

.log-entry {
  margin-bottom: 4px;
  color: #495057;
}

.clear-logs-btn {
  margin-top: 10px;
  background: #6c757d;
  color: white;
  font-size: 12px;
  padding: 6px 12px;
}

.clear-logs-btn:hover {
  background: #545b62;
}

/* 使用说明面板 */
.instructions-panel {
  grid-column: 1 / -1;
}

.instructions {
  background: white;
  padding: 20px;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.instructions ol {
  margin: 0 0 20px 0;
  padding-left: 20px;
}

.instructions li {
  margin-bottom: 8px;
  color: #495057;
}

.instructions strong {
  color: #343a40;
}

.tips {
  background: #e7f3ff;
  padding: 15px;
  border-radius: 6px;
  border-left: 4px solid #007bff;
}

.tips h4 {
  margin: 0 0 10px 0;
  color: #007bff;
}

.tips ul {
  margin: 0;
  padding-left: 20px;
}

.tips li {
  margin-bottom: 6px;
  color: #495057;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .button-group {
    flex-direction: column;
  }

  .item-details {
    flex-direction: column;
    gap: 4px;
  }

  .app-header h1 {
    font-size: 2rem;
  }
}
