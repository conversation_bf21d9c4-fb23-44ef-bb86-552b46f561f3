# Changelog


## v0.0.21

[compare changes](https://github.com/Sunny-117/browser-storage-lru-cleaner/compare/v0.0.20...v0.0.21)

## v0.0.20

[compare changes](https://github.com/Sunny-117/browser-storage-lru-cleaner/compare/v0.0.19...v0.0.20)

### 🏡 Chore

- 🤖 enQuotaExceeded ([0872df8](https://github.com/Sunny-117/browser-storage-lru-cleaner/commit/0872df8))

### ❤️ Contributors

- Sunny-117 <<EMAIL>>

## v0.0.19

[compare changes](https://github.com/Sunny-117/browser-storage-lru-cleaner/compare/v0.0.18...v0.0.19)

### 🚀 Enhancements

- **build:** Robuild ([00c82e0](https://github.com/Sunny-117/browser-storage-lru-cleaner/commit/00c82e0))
- 🎸 超出限额直接clear ([7a51887](https://github.com/Sunny-117/browser-storage-lru-cleaner/commit/7a51887))

### 🏡 Chore

- **release:** V0.0.18 ([f913916](https://github.com/Sunny-117/browser-storage-lru-cleaner/commit/f913916))
- Robuild script ([bd47940](https://github.com/Sunny-117/browser-storage-lru-cleaner/commit/bd47940))

### ❤️ Contributors

- Sunny-117 <<EMAIL>>

## v0.0.18

[compare changes](https://github.com/Sunny-117/browser-storage-lru-cleaner/compare/v0.0.17...v0.0.18)

### 🚀 Enhancements

- CleanupThreshold default 1 ([8fb79ef](https://github.com/Sunny-117/browser-storage-lru-cleaner/commit/8fb79ef))

### 🔥 Performance

- ⚡️ build by robuild ([b2bd5d9](https://github.com/Sunny-117/browser-storage-lru-cleaner/commit/b2bd5d9))

### 🏡 Chore

- 🤖 remove logs ([dd75f2b](https://github.com/Sunny-117/browser-storage-lru-cleaner/commit/dd75f2b))
- Playground config ([de619d2](https://github.com/Sunny-117/browser-storage-lru-cleaner/commit/de619d2))

### ❤️ Contributors

- Sunny-117 <<EMAIL>>

## v0.0.17

[compare changes](https://github.com/Sunny-117/browser-storage-lru-cleaner/compare/v0.0.16...v0.0.17)

### 💅 Refactors

- 💡 存储适配器接口 ([a1d9e2c](https://github.com/Sunny-117/browser-storage-lru-cleaner/commit/a1d9e2c))
- 💡 exports ([4f76243](https://github.com/Sunny-117/browser-storage-lru-cleaner/commit/4f76243))
- 💡 debug logs ([ec22c9d](https://github.com/Sunny-117/browser-storage-lru-cleaner/commit/ec22c9d))

### 🏡 Chore

- 🤖 remove cleanupRatio ([cb6e19b](https://github.com/Sunny-117/browser-storage-lru-cleaner/commit/cb6e19b))

### ❤️ Contributors

- Sunny-117 <<EMAIL>>

## v0.0.16

[compare changes](https://github.com/Sunny-117/browser-storage-lru-cleaner/compare/v0.0.15...v0.0.16)

### 🚀 Enhancements

- Prompt ([c199e04](https://github.com/Sunny-117/browser-storage-lru-cleaner/commit/c199e04))
- Demo proxy storage ([a137e77](https://github.com/Sunny-117/browser-storage-lru-cleaner/commit/a137e77))
- 🎸 不重要的keys列表，智能插入会自动处理 ([123daa1](https://github.com/Sunny-117/browser-storage-lru-cleaner/commit/123daa1))
- 🎸 cleanupOrphanedRecords ([c2fe7d6](https://github.com/Sunny-117/browser-storage-lru-cleaner/commit/c2fe7d6))
- 🎸 为存量数据设置当前时间作为初始访问时间 ([a077ed0](https://github.com/Sunny-117/browser-storage-lru-cleaner/commit/a077ed0))

### ❤️ Contributors

- Sunny-117 <<EMAIL>>

## v0.0.15

[compare changes](https://github.com/Sunny-117/browser-storage-lru-cleaner/compare/v0.0.14...v0.0.15)

### 🚀 Enhancements

- 🎸 时间格式化 ([f64503f](https://github.com/Sunny-117/browser-storage-lru-cleaner/commit/f64503f))

### 📖 Documentation

- Design ([9b5001b](https://github.com/Sunny-117/browser-storage-lru-cleaner/commit/9b5001b))

### 🏡 Chore

- Remove npm-run-all ([d019879](https://github.com/Sunny-117/browser-storage-lru-cleaner/commit/d019879))
- 🤖 第三方库hooks useLocalStorage 拦截测试 ([1cda16f](https://github.com/Sunny-117/browser-storage-lru-cleaner/commit/1cda16f))
- Fix app ([3f82356](https://github.com/Sunny-117/browser-storage-lru-cleaner/commit/3f82356))

### ❤️ Contributors

- Sunny-117 <<EMAIL>>

## v0.0.14

[compare changes](https://github.com/Sunny-117/browser-storage-lru-cleaner/compare/v0.0.13...v0.0.14)

### 🩹 Fixes

- 🐛 remove bumpp ([29cd400](https://github.com/Sunny-117/browser-storage-lru-cleaner/commit/29cd400))

### ❤️ Contributors

- Sunny-117 <<EMAIL>>

## v0.0.12

[compare changes](https://github.com/Sunny-117/browser-storage-lru-cleaner/compare/v0.0.11...v0.0.12)

### 🩹 Fixes

- 🐛 remove push tag ([f238b47](https://github.com/Sunny-117/browser-storage-lru-cleaner/commit/f238b47))

### ❤️ Contributors

- Sunny-117 <<EMAIL>>

## v0.0.10

[compare changes](https://github.com/Sunny-117/browser-storage-lru-cleaner/compare/v0.0.9...v0.0.10)

## v0.0.8

[compare changes](https://github.com/Sunny-117/browser-storage-lru-cleaner/compare/v0.0.7...v0.0.8)

### 🏡 Chore

- 🤖 add changelogen ([8d30b0c](https://github.com/Sunny-117/browser-storage-lru-cleaner/commit/8d30b0c))

### ❤️ Contributors

- Sunny-117 <<EMAIL>>

